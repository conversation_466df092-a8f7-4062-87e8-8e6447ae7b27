using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;
using ForceFollowingGame.Core;

namespace ForceFollowingGame.Game
{
    /// <summary>
    /// 关卡管理器 - 管理游戏关卡的加载、切换和配置
    /// </summary>
    public class LevelManager : MonoBehaviour
    {
        public static LevelManager Instance { get; private set; }

        [Header("关卡配置")]
        [SerializeField] private List<GameLevel> predefinedLevels;
        [SerializeField] private int currentLevelIndex = 0;
        [SerializeField] private GameLevel currentLevel;

        [Header("自适应系统")]
        [SerializeField] private AdaptiveDifficultySystem adaptiveSystem;

        [Header("关卡事件")]
        public UnityEvent<GameLevel> OnLevelLoaded;
        public UnityEvent<GameLevel> OnLevelCompleted;
        public UnityEvent<int> OnLevelIndexChanged;

        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;

        void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeLevelManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        void Start()
        {
            InitializePredefinedLevels();
            if (predefinedLevels.Count > 0)
            {
                currentLevel = predefinedLevels[0];
            }
        }

        private void InitializeLevelManager()
        {
            if (adaptiveSystem == null)
            {
                adaptiveSystem = GetComponent<AdaptiveDifficultySystem>();
                if (adaptiveSystem == null)
                {
                    adaptiveSystem = gameObject.AddComponent<AdaptiveDifficultySystem>();
                }
            }

            if (enableDebugLogs)
                Debug.Log("[LevelManager] 关卡管理器初始化完成");
        }

        /// <summary>
        /// 初始化预定义关卡
        /// </summary>
        private void InitializePredefinedLevels()
        {
            predefinedLevels = new List<GameLevel>
            {
                new GameLevel(1, "热身运动", "保持轻微握力，适应游戏", 5f, TrajectoryType.SteadyHold, 20f, 0.1f),
                new GameLevel(2, "缓慢爬升", "跟随目标缓慢增加握力", 10f, TrajectoryType.SlowRamp, 30f, 0.3f),
                new GameLevel(3, "波浪起伏", "跟随波浪形轨迹变化", 15f, TrajectoryType.Wave, 35f, 0.5f),
                new GameLevel(4, "阶梯挑战", "快速适应不同握力水平", 20f, TrajectoryType.Steps, 40f, 0.7f),
                new GameLevel(5, "终极挑战", "复杂轨迹的终极考验", 25f, TrajectoryType.Challenge, 45f, 0.9f)
            };

            // 为每个关卡生成目标曲线
            foreach (var level in predefinedLevels)
            {
                level.targetCurve = TrajectoryGenerator.GenerateTargetCurve(level);
                
                // 设置关卡颜色
                level.levelColor = GetLevelColor(level.levelNumber);
            }

            if (enableDebugLogs)
                Debug.Log($"[LevelManager] 初始化了 {predefinedLevels.Count} 个预定义关卡");
        }

        /// <summary>
        /// 获取关卡颜色
        /// </summary>
        private Color GetLevelColor(int levelNumber)
        {
            Color[] levelColors = {
                new Color(0.2f, 0.8f, 0.2f), // 绿色 - 简单
                new Color(0.2f, 0.6f, 0.8f), // 蓝色 - 容易
                new Color(0.8f, 0.8f, 0.2f), // 黄色 - 中等
                new Color(0.8f, 0.4f, 0.2f), // 橙色 - 困难
                new Color(0.8f, 0.2f, 0.2f)  // 红色 - 极难
            };

            int colorIndex = Mathf.Clamp(levelNumber - 1, 0, levelColors.Length - 1);
            return levelColors[colorIndex];
        }

        /// <summary>
        /// 加载指定关卡
        /// </summary>
        public bool LoadLevel(int levelIndex)
        {
            if (levelIndex < 0)
            {
                Debug.LogError($"[LevelManager] 无效的关卡索引: {levelIndex}");
                return false;
            }

            GameLevel targetLevel;

            if (levelIndex < predefinedLevels.Count)
            {
                // 加载预定义关卡
                targetLevel = predefinedLevels[levelIndex];
            }
            else
            {
                // 生成自适应关卡
                targetLevel = adaptiveSystem.GenerateAdaptiveLevel(levelIndex + 1);
            }

            return LoadLevel(targetLevel, levelIndex);
        }

        /// <summary>
        /// 加载指定关卡对象
        /// </summary>
        public bool LoadLevel(GameLevel level, int levelIndex = -1)
        {
            if (level == null)
            {
                Debug.LogError("[LevelManager] 尝试加载空关卡");
                return false;
            }

            currentLevel = level;
            
            if (levelIndex >= 0)
            {
                currentLevelIndex = levelIndex;
            }

            if (enableDebugLogs)
                Debug.Log($"[LevelManager] 加载关卡: {level.levelName} (索引: {currentLevelIndex})");

            // 触发关卡加载事件
            OnLevelLoaded?.Invoke(currentLevel);
            OnLevelIndexChanged?.Invoke(currentLevelIndex);

            return true;
        }

        /// <summary>
        /// 加载下一关
        /// </summary>
        public GameLevel LoadNextLevel()
        {
            int nextIndex = currentLevelIndex + 1;
            
            if (LoadLevel(nextIndex))
            {
                return currentLevel;
            }

            return null;
        }

        /// <summary>
        /// 预览下一关（不加载）
        /// </summary>
        public GameLevel PeekNextLevel()
        {
            int nextIndex = currentLevelIndex + 1;

            if (nextIndex < predefinedLevels.Count)
            {
                return predefinedLevels[nextIndex];
            }
            else
            {
                // 生成自适应关卡预览
                return adaptiveSystem.GenerateAdaptiveLevel(nextIndex + 1);
            }
        }

        /// <summary>
        /// 完成当前关卡
        /// </summary>
        public void CompleteCurrentLevel(PerformanceMetrics performance)
        {
            if (currentLevel == null)
            {
                Debug.LogWarning("[LevelManager] 尝试完成空关卡");
                return;
            }

            if (enableDebugLogs)
                Debug.Log($"[LevelManager] 完成关卡: {currentLevel.levelName}, 得分: {performance.finalScore:F0}");

            // 记录性能到自适应系统
            adaptiveSystem.RecordPerformance(performance.accuracy / 100f);

            // 触发关卡完成事件
            OnLevelCompleted?.Invoke(currentLevel);
        }

        /// <summary>
        /// 重置关卡进度
        /// </summary>
        public void ResetProgress()
        {
            currentLevelIndex = 0;
            if (predefinedLevels.Count > 0)
            {
                LoadLevel(0);
            }

            adaptiveSystem.ResetPerformanceHistory();

            if (enableDebugLogs)
                Debug.Log("[LevelManager] 关卡进度已重置");
        }

        /// <summary>
        /// 获取当前关卡
        /// </summary>
        public GameLevel GetCurrentLevel()
        {
            return currentLevel;
        }

        /// <summary>
        /// 获取当前关卡索引
        /// </summary>
        public int GetCurrentLevelIndex()
        {
            return currentLevelIndex;
        }

        /// <summary>
        /// 获取总关卡数（包括自适应关卡）
        /// </summary>
        public int GetTotalLevelCount()
        {
            return predefinedLevels.Count; // 自适应关卡数量是无限的
        }

        /// <summary>
        /// 检查是否还有更多关卡
        /// </summary>
        public bool HasMoreLevels()
        {
            return true; // 由于有自适应关卡，总是有更多关卡
        }

        /// <summary>
        /// 检查是否是预定义关卡
        /// </summary>
        public bool IsPreDefinedLevel(int levelIndex)
        {
            return levelIndex < predefinedLevels.Count;
        }

        /// <summary>
        /// 获取关卡进度百分比
        /// </summary>
        public float GetProgressPercentage()
        {
            if (predefinedLevels.Count == 0) return 100f;
            
            float progress = (float)(currentLevelIndex + 1) / predefinedLevels.Count;
            return Mathf.Clamp01(progress) * 100f;
        }

        /// <summary>
        /// 获取所有预定义关卡
        /// </summary>
        public List<GameLevel> GetPredefinedLevels()
        {
            return new List<GameLevel>(predefinedLevels);
        }

        /// <summary>
        /// 添加自定义关卡
        /// </summary>
        public void AddCustomLevel(GameLevel customLevel)
        {
            if (customLevel == null) return;

            predefinedLevels.Add(customLevel);
            
            if (enableDebugLogs)
                Debug.Log($"[LevelManager] 添加自定义关卡: {customLevel.levelName}");
        }

        /// <summary>
        /// 获取关卡统计信息
        /// </summary>
        public string GetLevelStats()
        {
            return $"当前关卡: {currentLevelIndex + 1}, " +
                   $"预定义关卡: {predefinedLevels.Count}, " +
                   $"进度: {GetProgressPercentage():F1}%";
        }

        // 调试方法
        [ContextMenu("Load Next Level")]
        public void DebugLoadNextLevel()
        {
            LoadNextLevel();
        }

        [ContextMenu("Reset Progress")]
        public void DebugResetProgress()
        {
            ResetProgress();
        }

        [ContextMenu("Print Level Stats")]
        public void DebugPrintStats()
        {
            Debug.Log($"[LevelManager] {GetLevelStats()}");
        }
    }
}
