using UnityEngine;
using UnityEngine.Events;
using ForceFollowingGame.Core;
using ForceFollowingGame.Game;
using ForceFollowingGame.UI;
using ForceFollowingGame.Calibration;
using ForceFollowingGame.Analysis;
using ForceFollowingGame.Data;

namespace ForceFollowingGame.Core
{
    /// <summary>
    /// 游戏管理器 - 协调所有系统的主控制器
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        public static GameManager Instance { get; private set; }

        [Header("UI面板")]
        [SerializeField] private MainMenuUI mainMenuUI;
        [SerializeField] private DeviceConnectionUI deviceConnectionUI;
        [SerializeField] private MVCCalibrationGame mvcCalibrationUI;
        [SerializeField] private RESTCalibrationGame restCalibrationUI;
        [SerializeField] private GameplayUI gameplayUI;
        [SerializeField] private LevelTransitionUI levelTransitionUI;

        [Header("系统组件")]
        [SerializeField] private GameStateManager gameStateManager;
        [SerializeField] private BLEDeviceManager bleDeviceManager;
        [SerializeField] private LevelManager levelManager;
        [SerializeField] private PerformanceAnalyzer performanceAnalyzer;
        [SerializeField] private DataManager dataManager;

        [Header("游戏配置")]
        [SerializeField] private bool autoStartInEditor = true;
        [SerializeField] private string debugParticipantId = "DebugUser";

        [Header("事件")]
        public UnityEvent OnGameInitialized;
        public UnityEvent OnGameStarted;
        public UnityEvent OnGameCompleted;

        void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        void Start()
        {
            SetupEventListeners();
            
#if UNITY_EDITOR
            if (autoStartInEditor)
            {
                StartCoroutine(AutoStartInEditor());
            }
#endif
        }

        /// <summary>
        /// 初始化游戏
        /// </summary>
        private void InitializeGame()
        {
            // 确保所有必要的组件都存在
            EnsureRequiredComponents();
            
            // 初始化所有UI面板为非激活状态
            InitializeUIPanels();
            
            Debug.Log("[GameManager] 游戏管理器初始化完成");
            OnGameInitialized?.Invoke();
        }

        /// <summary>
        /// 确保必要组件存在
        /// </summary>
        private void EnsureRequiredComponents()
        {
            // 自动查找或创建必要的组件
            if (gameStateManager == null)
                gameStateManager = FindObjectOfType<GameStateManager>();

            if (bleDeviceManager == null)
                bleDeviceManager = FindObjectOfType<BLEDeviceManager>();

            if (levelManager == null)
                levelManager = FindObjectOfType<LevelManager>();

            if (performanceAnalyzer == null)
                performanceAnalyzer = FindObjectOfType<PerformanceAnalyzer>();

            if (dataManager == null)
                dataManager = FindObjectOfType<DataManager>();

            // 如果找不到，则创建
            if (gameStateManager == null)
            {
                GameObject go = new GameObject("GameStateManager");
                gameStateManager = go.AddComponent<GameStateManager>();
            }

            if (bleDeviceManager == null)
            {
                GameObject go = new GameObject("BLEDeviceManager");
                bleDeviceManager = go.AddComponent<BLEDeviceManager>();
            }

            if (levelManager == null)
            {
                GameObject go = new GameObject("LevelManager");
                levelManager = go.AddComponent<LevelManager>();
            }

            if (performanceAnalyzer == null)
            {
                GameObject go = new GameObject("PerformanceAnalyzer");
                performanceAnalyzer = go.AddComponent<PerformanceAnalyzer>();
            }

            if (dataManager == null)
            {
                GameObject go = new GameObject("DataManager");
                dataManager = go.AddComponent<DataManager>();
            }
        }

        /// <summary>
        /// 初始化UI面板
        /// </summary>
        private void InitializeUIPanels()
        {
            // 隐藏所有UI面板
            if (mainMenuUI != null) mainMenuUI.gameObject.SetActive(false);
            if (deviceConnectionUI != null) deviceConnectionUI.gameObject.SetActive(false);
            if (mvcCalibrationUI != null) mvcCalibrationUI.gameObject.SetActive(false);
            if (restCalibrationUI != null) restCalibrationUI.gameObject.SetActive(false);
            if (gameplayUI != null) gameplayUI.gameObject.SetActive(false);
            if (levelTransitionUI != null) levelTransitionUI.gameObject.SetActive(false);

            // 显示主菜单
            if (mainMenuUI != null)
                mainMenuUI.gameObject.SetActive(true);
        }

        /// <summary>
        /// 设置事件监听器
        /// </summary>
        private void SetupEventListeners()
        {
            // 游戏状态管理器事件
            if (gameStateManager != null)
            {
                gameStateManager.OnStateChanged.AddListener(OnGameStateChanged);
            }

            // 关卡管理器事件
            if (levelManager != null)
            {
                levelManager.OnLevelCompleted.AddListener(OnLevelCompleted);
            }
        }

        /// <summary>
        /// 游戏状态改变事件处理
        /// </summary>
        private void OnGameStateChanged(GameStateManager.GameState newState)
        {
            Debug.Log($"[GameManager] 游戏状态改变: {newState}");

            // 隐藏所有UI面板
            HideAllUIPanels();

            // 根据新状态显示对应的UI面板
            switch (newState)
            {
                case GameStateManager.GameState.MainMenu:
                    if (mainMenuUI != null) mainMenuUI.Show();
                    break;

                case GameStateManager.GameState.DeviceConnection:
                    if (deviceConnectionUI != null) deviceConnectionUI.Show();
                    break;

                case GameStateManager.GameState.MVCCalibration:
                    if (mvcCalibrationUI != null) mvcCalibrationUI.gameObject.SetActive(true);
                    break;

                case GameStateManager.GameState.RESTCalibration:
                    if (restCalibrationUI != null) restCalibrationUI.gameObject.SetActive(true);
                    break;

                case GameStateManager.GameState.GameLevels:
                    if (gameplayUI != null) gameplayUI.gameObject.SetActive(true);
                    break;

                case GameStateManager.GameState.LevelTransition:
                    if (levelTransitionUI != null) levelTransitionUI.gameObject.SetActive(true);
                    break;

                case GameStateManager.GameState.Tutorial:
                    // 跳过教程，直接进入MVC校准
                    if (gameStateManager != null)
                    {
                        gameStateManager.TransitionToState(GameStateManager.GameState.MVCCalibration);
                    }
                    break;

                case GameStateManager.GameState.Results:
                    // 显示最终结果或返回主菜单
                    if (gameStateManager != null)
                    {
                        gameStateManager.TransitionToState(GameStateManager.GameState.MainMenu);
                    }
                    break;
            }
        }

        /// <summary>
        /// 隐藏所有UI面板
        /// </summary>
        private void HideAllUIPanels()
        {
            if (mainMenuUI != null) mainMenuUI.Hide();
            if (deviceConnectionUI != null) deviceConnectionUI.Hide();
            if (mvcCalibrationUI != null) mvcCalibrationUI.gameObject.SetActive(false);
            if (restCalibrationUI != null) restCalibrationUI.gameObject.SetActive(false);
            if (gameplayUI != null) gameplayUI.gameObject.SetActive(false);
            if (levelTransitionUI != null) levelTransitionUI.Hide();
        }

        /// <summary>
        /// 关卡完成事件处理
        /// </summary>
        private void OnLevelCompleted(GameLevel level)
        {
            Debug.Log($"[GameManager] 关卡完成: {level.levelName}");

            // 分析性能并显示结果
            if (performanceAnalyzer != null && levelTransitionUI != null)
            {
                var performance = performanceAnalyzer.AnalyzeCurrentLevel();
                levelTransitionUI.ShowResults(performance);
            }
        }

        /// <summary>
        /// Editor模式下自动开始
        /// </summary>
        private System.Collections.IEnumerator AutoStartInEditor()
        {
            yield return new UnityEngine.WaitForSeconds(1f);

            Debug.Log("[GameManager] Editor模式自动开始游戏");

            // 设置调试参与者ID
            if (mainMenuUI != null)
            {
                mainMenuUI.SetParticipantId(debugParticipantId);
            }

            // 开始新会话
            if (dataManager != null)
            {
                dataManager.StartNewSession(debugParticipantId);
            }

            // 跳转到设备连接
            if (gameStateManager != null)
            {
                gameStateManager.TransitionToState(GameStateManager.GameState.DeviceConnection);
            }
        }

        /// <summary>
        /// 开始游戏
        /// </summary>
        public void StartGame(string participantId)
        {
            Debug.Log($"[GameManager] 开始游戏，参与者: {participantId}");

            OnGameStarted?.Invoke();

            // 开始新会话
            if (dataManager != null)
            {
                dataManager.StartNewSession(participantId);
            }
        }

        /// <summary>
        /// 完成游戏
        /// </summary>
        public void CompleteGame()
        {
            Debug.Log("[GameManager] 游戏完成");

            OnGameCompleted?.Invoke();

            // 保存最终数据
            if (dataManager != null)
            {
                dataManager.SaveCurrentSession();
            }
        }

        /// <summary>
        /// 获取游戏统计信息
        /// </summary>
        public string GetGameStats()
        {
            string stats = "游戏统计信息:\n";
            
            if (dataManager != null)
                stats += $"数据: {dataManager.GetDataStats()}\n";
            
            if (levelManager != null)
                stats += $"关卡: {levelManager.GetLevelStats()}\n";
            
            if (bleDeviceManager != null)
                stats += $"设备: {bleDeviceManager.GetDataStats()}\n";

            return stats;
        }

        // 调试方法
        [ContextMenu("Print Game Stats")]
        public void DebugPrintStats()
        {
            Debug.Log($"[GameManager] {GetGameStats()}");
        }

        [ContextMenu("Force Complete Game")]
        public void DebugCompleteGame()
        {
            CompleteGame();
        }

        [ContextMenu("Reset Game")]
        public void DebugResetGame()
        {
            if (gameStateManager != null)
            {
                gameStateManager.TransitionToState(GameStateManager.GameState.MainMenu);
            }
        }

        void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                // 应用暂停时保存数据
                if (dataManager != null)
                {
                    dataManager.SaveCurrentSession();
                }
            }
        }

        void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                // 失去焦点时保存数据
                if (dataManager != null)
                {
                    dataManager.SaveCurrentSession();
                }
            }
        }
    }
}
