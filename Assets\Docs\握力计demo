"""
握力计代码

"""

import asyncio
import sys
import time
from bleak import BleakClient, BleakScanner

# BLE Device Configuration
DEVICE_MAC = "3C:AB:72:6F:68:6D"
NOTIFY_CHAR_UUID = "9ECADC24-0EE5-A9E0-93F3-A3B50300406E"

data_buffer = bytearray()

def parse_pressure_data(data):
    global data_buffer
    data_buffer.extend(data)
    readings = []

    while len(data_buffer) >= 6:
        header_pos = -1
        for i in range(len(data_buffer) - 5):
            if data_buffer[i] == 0x40 and data_buffer[i + 1] == 0x5C:
                header_pos = i
                break

        if header_pos == -1:
            data_buffer.clear()
            return readings

        if header_pos > 0:
            del data_buffer[:header_pos]

        if len(data_buffer) < 6:
            return readings

        packet = bytes(data_buffer[:6])
        del data_buffer[:6]

        pressure = (packet[2] << 16) | (packet[3] << 8) | packet[4]
        parity = packet[5]
        calc_parity = sum(packet[:5]) & 0xFF
        if parity == calc_parity:
            readings.append(pressure)

    return readings


def notification_handler(sender, data):
    pressures = parse_pressure_data(data)
    timestamp = time.time()
    print(f"{timestamp=} {pressures=}")


async def main():
    device = await BleakScanner.find_device_by_address(DEVICE_MAC, timeout=10.0)
    if not device:
        print(f"Device {DEVICE_MAC} not found.")
        return

    async with BleakClient(device.address) as client:
        services = client.services
        characteristic_found = False
        for service in services:
            for char in service.characteristics:
                if char.uuid.lower() == NOTIFY_CHAR_UUID.lower():
                    characteristic_found = True
                    break
            if characteristic_found:
                break

        if not characteristic_found:
            print(f"Notification characteristic {NOTIFY_CHAR_UUID} not found.")
            return

        await client.start_notify(NOTIFY_CHAR_UUID, notification_handler)

        print(f"Connected to device {DEVICE_MAC}. Streaming data to LSL...")

        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            await client.stop_notify(NOTIFY_CHAR_UUID)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        sys.exit(0)


"""真实数据
Connected to device 3C:AB:72:6F:68:6D. Streaming data to LSL...
timestamp=1753172428.2493405 pressures=[0, 0, 0]
timestamp=1753172428.2503424 pressures=[0, 0, 0]
timestamp=1753172428.309005 pressures=[0, 0, 0, 0]
timestamp=1753172428.4890263 pressures=[0, 0, 0]
timestamp=1753172428.5494766 pressures=[0, 0, 0]
timestamp=1753172428.5792673 pressures=[0, 0, 0, 0]
timestamp=1753172428.5802717 pressures=[0, 0, 0]
timestamp=1753172428.6393545 pressures=[0, 0, 0]
timestamp=1753172428.7292116 pressures=[0, 0, 0, 0]
timestamp=1753172428.7890341 pressures=[0, 0, 0]
timestamp=1753172428.939747 pressures=[0, 0, 0]
"""