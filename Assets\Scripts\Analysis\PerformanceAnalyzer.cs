using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using ForceFollowingGame.Core;

namespace ForceFollowingGame.Analysis
{
    /// <summary>
    /// 性能分析器 - 分析玩家的游戏表现并生成详细指标
    /// </summary>
    public class PerformanceAnalyzer : MonoBehaviour
    {
        public static PerformanceAnalyzer Instance { get; private set; }

        [Header("实时数据收集")]
        [SerializeField] private List<ForceDataPoint> currentLevelData;
        [SerializeField] private float levelStartTime;
        [SerializeField] private GameLevel currentLevel;

        [Header("分析参数")]
        [SerializeField] private float toleranceThreshold = 10f; // 容差阈值
        [SerializeField] private float responseTimeWindow = 2f;  // 反应时间窗口
        [SerializeField] private bool enableDetailedAnalysis = true;

        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;

        void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAnalyzer();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void InitializeAnalyzer()
        {
            currentLevelData = new List<ForceDataPoint>();
            
            if (enableDebugLogs)
                Debug.Log("[PerformanceAnalyzer] 性能分析器初始化完成");
        }

        /// <summary>
        /// 开始关卡分析
        /// </summary>
        /// <param name="level">关卡配置</param>
        public void StartLevelAnalysis(GameLevel level)
        {
            currentLevel = level;
            currentLevelData.Clear();
            levelStartTime = Time.time;

            if (enableDebugLogs)
                Debug.Log($"[PerformanceAnalyzer] 开始分析关卡: {level.levelName}");
        }

        /// <summary>
        /// 记录数据点
        /// </summary>
        /// <param name="actualForce">实际力量</param>
        /// <param name="targetForce">目标力量</param>
        public void RecordDataPoint(float actualForce, float targetForce)
        {
            var dataPoint = new ForceDataPoint(
                Time.time - levelStartTime,
                actualForce,
                targetForce,
                toleranceThreshold
            );

            currentLevelData.Add(dataPoint);
        }

        /// <summary>
        /// 分析当前关卡
        /// </summary>
        /// <returns>性能指标</returns>
        public PerformanceMetrics AnalyzeCurrentLevel()
        {
            if (currentLevelData.Count == 0)
            {
                Debug.LogWarning("[PerformanceAnalyzer] 没有数据可供分析");
                return GetDefaultMetrics();
            }

            var metrics = new PerformanceMetrics();

            // 计算基础指标
            metrics.accuracy = CalculateAccuracy();
            metrics.consistency = CalculateConsistency();
            metrics.responseTime = CalculateResponseTime();
            metrics.finalScore = CalculateFinalScore(metrics);
            metrics.stars = GetStarRating(metrics.finalScore);
            metrics.duration = currentLevelData.LastOrDefault().timestamp;

            // 计算详细指标
            if (enableDetailedAnalysis)
            {
                metrics.detailedMetrics = CalculateDetailedMetrics();
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[PerformanceAnalyzer] 分析完成 - 精度: {metrics.accuracy:F1}%, " +
                         $"一致性: {metrics.consistency:F1}%, 得分: {metrics.finalScore:F0}");
            }

            return metrics;
        }

        /// <summary>
        /// 计算精度
        /// </summary>
        private float CalculateAccuracy()
        {
            if (currentLevelData.Count == 0) return 0f;

            // 基于偏差的倒数计算精度
            float totalDeviation = currentLevelData.Sum(d => d.deviation);
            float avgDeviation = totalDeviation / currentLevelData.Count;
            
            // 将偏差转换为精度分数 (0-100%)
            float accuracy = Mathf.Max(0, 100f - avgDeviation * 2f);
            
            return Mathf.Clamp(accuracy, 0f, 100f);
        }

        /// <summary>
        /// 计算一致性
        /// </summary>
        private float CalculateConsistency()
        {
            if (currentLevelData.Count < 2) return 0f;

            // 计算力量变化的方差（越小越稳定）
            var forceValues = currentLevelData.Select(d => d.actualForce).ToArray();
            float mean = forceValues.Average();
            float variance = forceValues.Sum(f => Mathf.Pow(f - mean, 2)) / forceValues.Length;
            float stdDev = Mathf.Sqrt(variance);

            // 将标准差转换为一致性分数（0-100%）
            float consistencyScore = Mathf.Max(0, 100f - stdDev * 3f);
            
            return Mathf.Clamp(consistencyScore, 0f, 100f);
        }

        /// <summary>
        /// 计算反应时间
        /// </summary>
        private float CalculateResponseTime()
        {
            var responseDelays = new List<float>();

            for (int i = 1; i < currentLevelData.Count - 1; i++)
            {
                var prev = currentLevelData[i - 1];
                var curr = currentLevelData[i];

                // 检测目标变化
                float targetChange = Mathf.Abs(curr.targetForce - prev.targetForce);
                if (targetChange > 5f) // 目标发生显著变化
                {
                    // 寻找实际力量开始响应的时间点
                    for (int j = i; j < Math.Min(i + 20, currentLevelData.Count); j++)
                    {
                        float actualChange = Mathf.Abs(currentLevelData[j].actualForce - curr.actualForce);
                        if (actualChange > targetChange * 0.3f) // 开始响应
                        {
                            float delay = currentLevelData[j].timestamp - curr.timestamp;
                            responseDelays.Add(delay);
                            break;
                        }
                    }
                }
            }

            return responseDelays.Count > 0 ? responseDelays.Average() * 1000f : 0f; // 转换为毫秒
        }

        /// <summary>
        /// 计算最终得分
        /// </summary>
        private float CalculateFinalScore(PerformanceMetrics metrics)
        {
            // 综合评分公式
            float accuracyWeight = 0.5f;
            float consistencyWeight = 0.3f;
            float responseWeight = 0.2f;

            float accuracyScore = metrics.accuracy * accuracyWeight;
            float consistencyScore = metrics.consistency * consistencyWeight;
            float responseScore = Mathf.Max(0, 100f - metrics.responseTime / 10f) * responseWeight;

            float baseScore = accuracyScore + consistencyScore + responseScore;

            // 难度加成
            float difficultyBonus = currentLevel != null ? currentLevel.difficulty * 200f : 0f;

            return baseScore * 10f + difficultyBonus; // 基础分*10 + 难度奖励
        }

        /// <summary>
        /// 获取星级评定
        /// </summary>
        private int GetStarRating(float finalScore)
        {
            if (finalScore >= 900f) return 3;      // 3星：900+分
            if (finalScore >= 700f) return 2;      // 2星：700-899分
            if (finalScore >= 500f) return 1;      // 1星：500-699分
            return 0;                              // 0星：<500分
        }

        /// <summary>
        /// 计算详细指标
        /// </summary>
        private Dictionary<string, float> CalculateDetailedMetrics()
        {
            var detailed = new Dictionary<string, float>();

            if (currentLevelData.Count == 0) return detailed;

            // 容差内时间百分比
            int toleranceCount = currentLevelData.Count(d => d.isInTolerance);
            detailed["TolerancePercentage"] = (float)toleranceCount / currentLevelData.Count * 100f;

            // 最大偏差
            detailed["MaxDeviation"] = currentLevelData.Max(d => d.deviation);

            // 平均偏差
            detailed["AvgDeviation"] = currentLevelData.Average(d => d.deviation);

            // 力量范围利用率
            float minForce = currentLevelData.Min(d => d.actualForce);
            float maxForce = currentLevelData.Max(d => d.actualForce);
            detailed["ForceRange"] = maxForce - minForce;

            // 目标跟随率
            detailed["TargetFollowingRate"] = CalculateTargetFollowingRate();

            // 稳定性指数
            detailed["StabilityIndex"] = CalculateStabilityIndex();

            // 反应敏感度
            detailed["ResponseSensitivity"] = CalculateResponseSensitivity();

            return detailed;
        }

        /// <summary>
        /// 计算目标跟随率
        /// </summary>
        private float CalculateTargetFollowingRate()
        {
            if (currentLevelData.Count < 2) return 0f;

            int followingCount = 0;
            
            for (int i = 1; i < currentLevelData.Count; i++)
            {
                var prev = currentLevelData[i - 1];
                var curr = currentLevelData[i];

                float targetDirection = curr.targetForce - prev.targetForce;
                float actualDirection = curr.actualForce - prev.actualForce;

                // 检查是否跟随目标方向
                if (Mathf.Sign(targetDirection) == Mathf.Sign(actualDirection) || 
                    Mathf.Abs(targetDirection) < 1f)
                {
                    followingCount++;
                }
            }

            return (float)followingCount / (currentLevelData.Count - 1) * 100f;
        }

        /// <summary>
        /// 计算稳定性指数
        /// </summary>
        private float CalculateStabilityIndex()
        {
            if (currentLevelData.Count < 10) return 0f;

            // 计算连续10个点的方差，取平均值
            var stabilities = new List<float>();
            
            for (int i = 0; i <= currentLevelData.Count - 10; i++)
            {
                var segment = currentLevelData.Skip(i).Take(10).Select(d => d.actualForce).ToArray();
                float mean = segment.Average();
                float variance = segment.Sum(f => Mathf.Pow(f - mean, 2)) / segment.Length;
                stabilities.Add(variance);
            }

            float avgVariance = stabilities.Average();
            return Mathf.Max(0, 100f - avgVariance);
        }

        /// <summary>
        /// 计算反应敏感度
        /// </summary>
        private float CalculateResponseSensitivity()
        {
            if (currentLevelData.Count < 5) return 0f;

            var sensitivities = new List<float>();

            for (int i = 2; i < currentLevelData.Count - 2; i++)
            {
                var window = currentLevelData.Skip(i - 2).Take(5).ToArray();
                
                float targetChange = window.Max(d => d.targetForce) - window.Min(d => d.targetForce);
                float actualChange = window.Max(d => d.actualForce) - window.Min(d => d.actualForce);

                if (targetChange > 1f)
                {
                    float sensitivity = actualChange / targetChange;
                    sensitivities.Add(sensitivity);
                }
            }

            return sensitivities.Count > 0 ? sensitivities.Average() * 100f : 0f;
        }

        /// <summary>
        /// 获取默认指标
        /// </summary>
        private PerformanceMetrics GetDefaultMetrics()
        {
            return new PerformanceMetrics(0f, 0f, 0f, 0f, 0, 0f);
        }

        /// <summary>
        /// 获取当前关卡数据
        /// </summary>
        public List<ForceDataPoint> GetCurrentLevelData()
        {
            return new List<ForceDataPoint>(currentLevelData);
        }

        /// <summary>
        /// 清除当前数据
        /// </summary>
        public void ClearCurrentData()
        {
            currentLevelData.Clear();
            
            if (enableDebugLogs)
                Debug.Log("[PerformanceAnalyzer] 当前数据已清除");
        }

        /// <summary>
        /// 获取分析统计信息
        /// </summary>
        public string GetAnalysisStats()
        {
            return $"数据点数: {currentLevelData.Count}, " +
                   $"关卡: {(currentLevel != null ? currentLevel.levelName : "无")}, " +
                   $"持续时间: {(currentLevelData.Count > 0 ? currentLevelData.Last().timestamp : 0):F1}s";
        }

        // 调试方法
        [ContextMenu("Print Analysis Stats")]
        public void DebugPrintStats()
        {
            Debug.Log($"[PerformanceAnalyzer] {GetAnalysisStats()}");
        }

        [ContextMenu("Analyze Current Level")]
        public void DebugAnalyzeLevel()
        {
            var metrics = AnalyzeCurrentLevel();
            Debug.Log($"[PerformanceAnalyzer] 分析结果: 精度={metrics.accuracy:F1}%, " +
                     $"一致性={metrics.consistency:F1}%, 得分={metrics.finalScore:F0}, 星级={metrics.stars}");
        }

        [ContextMenu("Clear Current Data")]
        public void DebugClearData()
        {
            ClearCurrentData();
        }
    }
}
