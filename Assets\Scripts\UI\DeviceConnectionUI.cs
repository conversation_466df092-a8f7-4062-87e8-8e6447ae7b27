using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;
using ForceFollowingGame.Core;

namespace ForceFollowingGame.UI
{
    /// <summary>
    /// 设备连接界面 - 处理BLE设备连接的用户界面
    /// </summary>
    public class DeviceConnectionUI : MonoBehaviour
    {
        [Header("UI元素")]
        [SerializeField] private Text statusText;                  // 状态文字
        [SerializeField] private Text deviceInfoText;              // 设备信息
        [SerializeField] private Button connectButton;             // 连接按钮
        [SerializeField] private Button skipButton;                // 跳过按钮（调试用）
        [SerializeField] private Button backButton;                // 返回按钮
        [SerializeField] private Slider connectionProgress;        // 连接进度条

        [Header("视觉反馈")]
        [SerializeField] private Image statusIndicator;            // 状态指示器
        [SerializeField] private ParticleSystem connectionEffect;  // 连接特效
        [SerializeField] private Color connectingColor = Color.yellow;
        [SerializeField] private Color connectedColor = Color.green;
        [SerializeField] private Color errorColor = Color.red;

        [Header("动画设置")]
        [SerializeField] private float connectionTimeout = 10f;    // 连接超时时间
        [SerializeField] private float progressUpdateSpeed = 2f;   // 进度更新速度

        [Header("音效")]
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioClip connectingSound;
        [SerializeField] private AudioClip connectedSound;
        [SerializeField] private AudioClip errorSound;

        [Header("事件")]
        public UnityEvent OnConnectionSuccessful;
        public UnityEvent OnConnectionFailed;
        public UnityEvent OnConnectionSkipped;
        public UnityEvent OnBackRequested;

        private bool isConnecting = false;
        private Coroutine connectionCoroutine;

        void Start()
        {
            InitializeUI();
            SetupEventListeners();
        }

        void OnEnable()
        {
            // 自动开始连接
            StartConnection();
        }

        void OnDisable()
        {
            // 停止连接协程
            if (connectionCoroutine != null)
            {
                StopCoroutine(connectionCoroutine);
                connectionCoroutine = null;
            }
        }

        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            if (statusText != null)
                statusText.text = "准备连接设备...";

            if (deviceInfoText != null && BLEDeviceManager.Instance != null)
                deviceInfoText.text = $"目标设备: {BLEDeviceManager.Instance.targetDeviceMAC}";

            if (connectionProgress != null)
                connectionProgress.value = 0f;

            if (statusIndicator != null)
                statusIndicator.color = Color.gray;

            UpdateButtonStates(false);
        }

        /// <summary>
        /// 设置事件监听器
        /// </summary>
        private void SetupEventListeners()
        {
            if (connectButton != null)
                connectButton.onClick.AddListener(StartConnection);

            if (skipButton != null)
                skipButton.onClick.AddListener(SkipConnection);

            if (backButton != null)
                backButton.onClick.AddListener(GoBack);

            // 订阅BLE设备事件
            if (BLEDeviceManager.Instance != null)
            {
                BLEDeviceManager.Instance.OnDeviceConnected.AddListener(OnDeviceConnected);
                BLEDeviceManager.Instance.OnDeviceDisconnected.AddListener(OnDeviceDisconnected);
                BLEDeviceManager.Instance.OnConnectionError.AddListener(OnConnectionError);
            }
        }

        /// <summary>
        /// 开始连接
        /// </summary>
        public void StartConnection()
        {
            if (isConnecting) return;

            if (BLEDeviceManager.Instance == null)
            {
                ShowError("BLE设备管理器未初始化");
                return;
            }

            isConnecting = true;
            connectionCoroutine = StartCoroutine(ConnectionProcess());
        }

        /// <summary>
        /// 连接过程协程
        /// </summary>
        private IEnumerator ConnectionProcess()
        {
            // 更新UI状态
            if (statusText != null)
                statusText.text = "正在搜索设备...";

            if (statusIndicator != null)
                statusIndicator.color = connectingColor;

            if (audioSource != null && connectingSound != null)
                audioSource.PlayOneShot(connectingSound);

            if (connectionEffect != null)
                connectionEffect.Play();

            UpdateButtonStates(true);

            // 模拟连接进度
            float elapsed = 0f;
            while (elapsed < connectionTimeout && isConnecting)
            {
                elapsed += Time.deltaTime;
                
                if (connectionProgress != null)
                {
                    float progress = elapsed / connectionTimeout;
                    connectionProgress.value = Mathf.Lerp(connectionProgress.value, progress, 
                        Time.deltaTime * progressUpdateSpeed);
                }

                // 更新状态文字
                if (statusText != null)
                {
                    if (elapsed < connectionTimeout * 0.3f)
                        statusText.text = "正在搜索设备...";
                    else if (elapsed < connectionTimeout * 0.7f)
                        statusText.text = "正在建立连接...";
                    else
                        statusText.text = "正在验证连接...";
                }

                yield return null;
            }

            // 尝试实际连接
            bool connectionResult = false;
            try
            {
                // connectionResult = await BLEDeviceManager.Instance.ConnectToDevice();
                connectionResult = false;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[DeviceConnectionUI] 连接异常: {e.Message}");
                connectionResult = false;
            }

            if (connectionResult)
            {
                OnDeviceConnected();
            }
            else if (isConnecting) // 只有在没有被取消的情况下才显示错误
            {
                ShowError("连接超时，请检查设备是否开启");
            }

            isConnecting = false;
        }

        /// <summary>
        /// 设备连接成功事件
        /// </summary>
        private void OnDeviceConnected()
        {
            isConnecting = false;

            if (statusText != null)
                statusText.text = "设备连接成功!";

            if (statusIndicator != null)
                statusIndicator.color = connectedColor;

            if (connectionProgress != null)
                connectionProgress.value = 1f;

            if (audioSource != null && connectedSound != null)
                audioSource.PlayOneShot(connectedSound);

            if (connectionEffect != null)
                connectionEffect.Stop();

            OnConnectionSuccessful?.Invoke();

            Debug.Log("[DeviceConnectionUI] 设备连接成功");

            // 2秒后自动进入下一阶段
            StartCoroutine(ProceedToNextStage(2f));
        }

        /// <summary>
        /// 设备断开连接事件
        /// </summary>
        private void OnDeviceDisconnected()
        {
            if (statusText != null)
                statusText.text = "设备连接断开";

            if (statusIndicator != null)
                statusIndicator.color = Color.gray;

            UpdateButtonStates(false);
        }

        /// <summary>
        /// 连接错误事件
        /// </summary>
        private void OnConnectionError(string error)
        {
            ShowError($"连接失败: {error}");
        }

        /// <summary>
        /// 显示错误
        /// </summary>
        private void ShowError(string errorMessage)
        {
            isConnecting = false;

            if (statusText != null)
                statusText.text = errorMessage;

            if (statusIndicator != null)
                statusIndicator.color = errorColor;

            if (connectionProgress != null)
                connectionProgress.value = 0f;

            if (audioSource != null && errorSound != null)
                audioSource.PlayOneShot(errorSound);

            if (connectionEffect != null)
                connectionEffect.Stop();

            UpdateButtonStates(false);
            OnConnectionFailed?.Invoke();

            Debug.LogError($"[DeviceConnectionUI] {errorMessage}");
        }

        /// <summary>
        /// 跳过连接（调试用）
        /// </summary>
        private void SkipConnection()
        {
            isConnecting = false;

            if (connectionCoroutine != null)
            {
                StopCoroutine(connectionCoroutine);
                connectionCoroutine = null;
            }

            // 模拟连接成功
            if (BLEDeviceManager.Instance != null)
            {
                // 在Editor模式下启用模拟模式
#if UNITY_EDITOR
                BLEDeviceManager.Instance.SimulateConnection();
#endif
            }

            OnConnectionSkipped?.Invoke();
            
            Debug.Log("[DeviceConnectionUI] 跳过设备连接");

            StartCoroutine(ProceedToNextStage(0.5f));
        }

        /// <summary>
        /// 返回主菜单
        /// </summary>
        private void GoBack()
        {
            isConnecting = false;

            if (connectionCoroutine != null)
            {
                StopCoroutine(connectionCoroutine);
                connectionCoroutine = null;
            }

            OnBackRequested?.Invoke();

            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.TransitionToState(GameStateManager.GameState.MainMenu);
            }
        }

        /// <summary>
        /// 进入下一阶段
        /// </summary>
        private IEnumerator ProceedToNextStage(float delay)
        {
            yield return new WaitForSeconds(delay);

            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.TransitionToState(GameStateManager.GameState.Tutorial);
            }
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStates(bool connecting)
        {
            if (connectButton != null)
            {
                connectButton.interactable = !connecting;
                Text buttonText = connectButton.GetComponentInChildren<Text>();
                if (buttonText != null)
                {
                    buttonText.text = connecting ? "连接中..." : "重新连接";
                }
            }

            if (skipButton != null)
                skipButton.interactable = !connecting;

            if (backButton != null)
                backButton.interactable = !connecting;
        }

        /// <summary>
        /// 显示界面
        /// </summary>
        public void Show()
        {
            gameObject.SetActive(true);
        }

        /// <summary>
        /// 隐藏界面
        /// </summary>
        public void Hide()
        {
            gameObject.SetActive(false);
        }

        void OnDestroy()
        {
            // 取消事件订阅
            if (BLEDeviceManager.Instance != null)
            {
                BLEDeviceManager.Instance.OnDeviceConnected.RemoveListener(OnDeviceConnected);
                BLEDeviceManager.Instance.OnDeviceDisconnected.RemoveListener(OnDeviceDisconnected);
                BLEDeviceManager.Instance.OnConnectionError.RemoveListener(OnConnectionError);
            }
        }

        // 调试方法
        [ContextMenu("Test Connection Success")]
        public void TestConnectionSuccess()
        {
            OnDeviceConnected();
        }

        [ContextMenu("Test Connection Error")]
        public void TestConnectionError()
        {
            ShowError("测试连接错误");
        }

        [ContextMenu("Test Skip Connection")]
        public void TestSkipConnection()
        {
            SkipConnection();
        }
    }
}
