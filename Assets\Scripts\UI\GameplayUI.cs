using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using ForceFollowingGame.Core;
using ForceFollowingGame.Game;
using ForceFollowingGame.Analysis;
using ForceFollowingGame.Data;

namespace ForceFollowingGame.UI
{
    /// <summary>
    /// 游戏界面UI - 主游戏界面的视觉反馈和交互
    /// </summary>
    public class GameplayUI : MonoBehaviour
    {
        [Header("游戏视觉元素")]
        [SerializeField] private RectTransform gameArea;           // 游戏区域
        [SerializeField] private LineRenderer targetPath;          // 目标轨迹线
        [SerializeField] private Transform playerBird;             // 玩家"小鸟"
        [SerializeField] private ParticleSystem successEffect;     // 成功特效
        [SerializeField] private ParticleSystem trailEffect;       // 轨迹特效

        [Header("UI元素")]
        [SerializeField] private Text levelNameText;               // 关卡名称
        [SerializeField] private Text scoreText;                   // 当前得分
        [SerializeField] private Slider progressBar;               // 进度条
        [SerializeField] private Slider forceBar;                  // 当前力量条
        [SerializeField] private Text timeRemainingText;           // 剩余时间
        [SerializeField] private Text forceValueText;              // 力量数值显示

        [Header("视觉反馈")]
        [SerializeField] private Image accuracyIndicator;          // 精度指示器
        [SerializeField] private Color perfectColor = Color.green; // 完美跟随颜色
        [SerializeField] private Color goodColor = Color.yellow;   // 良好跟随颜色
        [SerializeField] private Color poorColor = Color.red;      // 差劲跟随颜色

        [Header("游戏状态")]
        [SerializeField] private float currentForce = 0f;
        [SerializeField] private float targetForce = 0f;
        [SerializeField] private float currentScore = 0f;
        [SerializeField] private float gameTime = 0f;
        [SerializeField] private float totalDuration = 30f;

        [Header("小鸟控制")]
        [SerializeField] private float birdSmoothness = 10f;
        [SerializeField] private float rotationSmoothness = 5f;
        [SerializeField] private float maxRotation = 30f;

        [Header("轨迹显示")]
        [SerializeField] private int pathPoints = 100;
        [SerializeField] private float pathWidth = 0.1f;
        [SerializeField] private Color completedPathColor = Color.gray;
        [SerializeField] private Color upcomingPathColor = Color.white;

        private Camera gameCamera;
        private Vector3 lastBirdPosition;
        private float previousForce = 0f;
        private bool isGameActive = false;

        void Start()
        {
            gameCamera = Camera.main;
            InitializeUI();
        }

        void Update()
        {
            if (GameStateManager.Instance != null && 
                GameStateManager.Instance.IsInState(GameStateManager.GameState.GameLevels) && 
                isGameActive)
            {
                UpdateGameplay();
                UpdateVisuals();
                UpdateUI();
            }
        }

        void OnEnable()
        {
            // 订阅事件
            if (BLEDeviceManager.Instance != null)
            {
                BLEDeviceManager.Instance.OnForceDataReceived.AddListener(OnForceDataReceived);
            }

            if (LevelManager.Instance != null)
            {
                LevelManager.Instance.OnLevelLoaded.AddListener(OnLevelLoaded);
            }
        }

        void OnDisable()
        {
            // 取消订阅事件
            if (BLEDeviceManager.Instance != null)
            {
                BLEDeviceManager.Instance.OnForceDataReceived.RemoveListener(OnForceDataReceived);
            }

            if (LevelManager.Instance != null)
            {
                LevelManager.Instance.OnLevelLoaded.RemoveListener(OnLevelLoaded);
            }
        }

        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            if (targetPath != null)
            {
                targetPath.positionCount = pathPoints;
                targetPath.startWidth = pathWidth;
                targetPath.endWidth = pathWidth;
            }

            if (playerBird != null)
            {
                lastBirdPosition = playerBird.localPosition;
            }

            ResetGameState();
        }

        /// <summary>
        /// 重置游戏状态
        /// </summary>
        private void ResetGameState()
        {
            currentScore = 0f;
            gameTime = 0f;
            currentForce = 0f;
            targetForce = 0f;
            previousForce = 0f;
            isGameActive = false;
        }

        /// <summary>
        /// 关卡加载事件处理
        /// </summary>
        private void OnLevelLoaded(GameLevel level)
        {
            if (level == null) return;

            totalDuration = level.duration;
            ResetGameState();
            
            // 开始性能分析
            if (PerformanceAnalyzer.Instance != null)
            {
                PerformanceAnalyzer.Instance.StartLevelAnalysis(level);
            }

            isGameActive = true;

            Debug.Log($"[GameplayUI] 关卡加载: {level.levelName}");
        }

        /// <summary>
        /// 力量数据接收事件处理
        /// </summary>
        public void OnForceDataReceived(float forceValue)
        {
            // 转换为MVC百分比
            if (DataManager.Instance != null && DataManager.Instance.IsCalibrationValid())
            {
                currentForce = DataManager.Instance.ConvertToMVCPercent(forceValue);
            }
            else
            {
                currentForce = forceValue; // 使用原始值
            }
        }

        /// <summary>
        /// 更新游戏逻辑
        /// </summary>
        private void UpdateGameplay()
        {
            gameTime += Time.deltaTime;

            // 获取当前目标力量
            var currentLevel = LevelManager.Instance?.GetCurrentLevel();
            if (currentLevel != null)
            {
                float normalizedTime = gameTime / totalDuration;
                targetForce = currentLevel.GetTargetForceAtTime(normalizedTime);
            }

            // 计算得分
            float deviation = Mathf.Abs(currentForce - targetForce);
            float frameScore = Mathf.Max(0, 100f - deviation * 2f); // 偏差越小得分越高
            currentScore += frameScore * Time.deltaTime;

            // 记录数据点用于分析
            if (PerformanceAnalyzer.Instance != null)
            {
                PerformanceAnalyzer.Instance.RecordDataPoint(currentForce, targetForce);
            }

            // 检查关卡是否完成
            if (gameTime >= totalDuration)
            {
                CompleteLevel();
            }
        }

        /// <summary>
        /// 更新视觉效果
        /// </summary>
        private void UpdateVisuals()
        {
            UpdateBirdPosition();
            UpdateTargetPath();
            UpdateAccuracyFeedback();
        }

        /// <summary>
        /// 更新小鸟位置
        /// </summary>
        private void UpdateBirdPosition()
        {
            if (playerBird == null || gameArea == null) return;

            // 将力量值映射到屏幕位置
            float screenHeight = gameArea.rect.height;
            float birdY = Mathf.Lerp(0.1f * screenHeight, 0.9f * screenHeight, currentForce / 100f);

            Vector3 targetPos = new Vector3(playerBird.localPosition.x, birdY, 0);
            playerBird.localPosition = Vector3.Lerp(playerBird.localPosition, targetPos, 
                Time.deltaTime * birdSmoothness);

            // 小鸟旋转角度（根据力量变化）
            float forceChangeRate = (currentForce - previousForce) / Time.deltaTime;
            float rotation = Mathf.Clamp(forceChangeRate * 2f, -maxRotation, maxRotation);
            
            Quaternion targetRotation = Quaternion.Euler(0, 0, rotation);
            playerBird.rotation = Quaternion.Lerp(playerBird.rotation, targetRotation, 
                Time.deltaTime * rotationSmoothness);

            // 更新轨迹特效
            if (trailEffect != null)
            {
                var emission = trailEffect.emission;
                emission.rateOverTime = Mathf.Lerp(10f, 50f, currentForce / 100f);
            }

            previousForce = currentForce;
        }

        /// <summary>
        /// 更新目标路径
        /// </summary>
        private void UpdateTargetPath()
        {
            if (targetPath == null || gameArea == null) return;

            var currentLevel = LevelManager.Instance?.GetCurrentLevel();
            if (currentLevel == null) return;

            Vector3[] positions = new Vector3[pathPoints];
            float completionRatio = gameTime / totalDuration;

            for (int i = 0; i < pathPoints; i++)
            {
                float t = (float)i / (pathPoints - 1);
                float pathForce = currentLevel.GetTargetForceAtTime(t);
                float pathY = Mathf.Lerp(0.1f * gameArea.rect.height, 0.9f * gameArea.rect.height, 
                    pathForce / 100f);
                float pathX = Mathf.Lerp(-gameArea.rect.width / 2, gameArea.rect.width / 2, t);

                positions[i] = new Vector3(pathX, pathY, 0);
            }

            targetPath.positionCount = pathPoints;
            targetPath.SetPositions(positions);

            // 路径颜色渐变（已完成部分vs未完成部分）
            UpdatePathGradient(completionRatio);
        }

        /// <summary>
        /// 更新路径渐变
        /// </summary>
        private void UpdatePathGradient(float completionRatio)
        {
            if (targetPath == null) return;

            Gradient pathGradient = new Gradient();
            var colorKeys = new GradientColorKey[3];
            var alphaKeys = new GradientAlphaKey[2];

            // 设置颜色键
            colorKeys[0] = new GradientColorKey(completedPathColor, 0f);
            colorKeys[1] = new GradientColorKey(completedPathColor, completionRatio);
            colorKeys[2] = new GradientColorKey(upcomingPathColor, 1f);

            // 设置透明度键
            alphaKeys[0] = new GradientAlphaKey(1f, 0f);
            alphaKeys[1] = new GradientAlphaKey(1f, 1f);

            pathGradient.SetKeys(colorKeys, alphaKeys);
            targetPath.colorGradient = pathGradient;
        }

        /// <summary>
        /// 更新精度反馈
        /// </summary>
        private void UpdateAccuracyFeedback()
        {
            if (accuracyIndicator == null) return;

            float deviation = Mathf.Abs(currentForce - targetForce);
            Color feedbackColor;

            if (deviation < 5f)
                feedbackColor = perfectColor;
            else if (deviation < 15f)
                feedbackColor = goodColor;
            else
                feedbackColor = poorColor;

            accuracyIndicator.color = Color.Lerp(accuracyIndicator.color, feedbackColor, 
                Time.deltaTime * 5f);

            // 触发特效
            if (deviation < 3f && successEffect != null && !successEffect.isPlaying)
            {
                successEffect.Play();
            }
        }

        /// <summary>
        /// 更新UI显示
        /// </summary>
        private void UpdateUI()
        {
            var currentLevel = LevelManager.Instance?.GetCurrentLevel();
            
            if (levelNameText != null && currentLevel != null)
                levelNameText.text = currentLevel.levelName;

            if (scoreText != null)
                scoreText.text = $"得分: {currentScore:F0}";

            if (progressBar != null)
                progressBar.value = gameTime / totalDuration;

            if (forceBar != null)
                forceBar.value = currentForce / 100f;

            if (timeRemainingText != null)
            {
                float timeRemaining = Mathf.Max(0, totalDuration - gameTime);
                timeRemainingText.text = $"剩余: {timeRemaining:F1}s";
            }

            if (forceValueText != null)
                forceValueText.text = $"{currentForce:F1}%";
        }

        /// <summary>
        /// 完成关卡
        /// </summary>
        private void CompleteLevel()
        {
            isGameActive = false;

            // 分析性能
            var performance = PerformanceAnalyzer.Instance?.AnalyzeCurrentLevel() ?? 
                new PerformanceMetrics();

            // 保存关卡结果
            var currentLevel = LevelManager.Instance?.GetCurrentLevel();
            if (currentLevel != null && DataManager.Instance != null)
            {
                var rawData = PerformanceAnalyzer.Instance?.GetCurrentLevelData() ?? 
                    new System.Collections.Generic.List<ForceDataPoint>();
                
                DataManager.Instance.SaveLevelResult(
                    currentLevel.levelNumber,
                    currentLevel.levelName,
                    performance,
                    rawData,
                    currentLevel
                );
            }

            // 记录性能到关卡管理器
            if (LevelManager.Instance != null)
            {
                LevelManager.Instance.CompleteCurrentLevel(performance);
            }

            // 转换到关卡间休息界面
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.TransitionToState(GameStateManager.GameState.LevelTransition);
            }

            Debug.Log($"[GameplayUI] 关卡完成，得分: {performance.finalScore:F0}");
        }

        /// <summary>
        /// 获取当前游戏状态
        /// </summary>
        public bool IsGameActive()
        {
            return isGameActive;
        }

        /// <summary>
        /// 获取当前得分
        /// </summary>
        public float GetCurrentScore()
        {
            return currentScore;
        }

        /// <summary>
        /// 获取游戏进度
        /// </summary>
        public float GetGameProgress()
        {
            return totalDuration > 0 ? gameTime / totalDuration : 0f;
        }

        // 调试方法
        [ContextMenu("Complete Level")]
        public void DebugCompleteLevel()
        {
            CompleteLevel();
        }

        [ContextMenu("Reset Game")]
        public void DebugResetGame()
        {
            ResetGameState();
        }
    }
}
