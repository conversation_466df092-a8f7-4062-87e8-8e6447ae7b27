using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;
using ForceFollowingGame.Core;
using ForceFollowingGame.Data;

namespace ForceFollowingGame.Calibration
{
    /// <summary>
    /// REST校准游戏 - 游戏化的静息状态测量
    /// </summary>
    public class RESTCalibrationGame : MonoBehaviour
    {
        [Header("校准参数")]
        [SerializeField] private float calibrationDuration = 10f;    // 校准持续时间
        [SerializeField] private float stabilityThreshold = 2f;      // 稳定性阈值
        [SerializeField] private float targetStabilityTime = 5f;     // 需要保持稳定的时间

        [Header("UI元素")]
        [SerializeField] private Text instructionText;               // 指导文字
        [SerializeField] private Slider stabilityBar;                // 稳定性进度条
        [SerializeField] private Text forceValueText;                // 当前力量值
        [SerializeField] private Image stabilityIndicator;           // 稳定性指示器
        [SerializeField] private ParticleSystem relaxationEffect;    // 放松特效
        [SerializeField] private Button startButton;                 // 开始按钮

        [Header("视觉反馈")]
        [SerializeField] private Color stableColor = Color.green;
        [SerializeField] private Color unstableColor = Color.red;
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color completedColor = Color.green;

        [Header("音效")]
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioClip stabilitySound;
        [SerializeField] private AudioClip completionSound;

        private float currentRESTValue = 0f;
        private float stabilityTimer = 0f;
        private Queue<float> recentForceValues = new Queue<float>();
        private const int STABILITY_SAMPLE_SIZE = 50;
        private bool isCalibrationComplete = false;
        private bool isCalibrationActive = false;

        [Header("事件")]
        public UnityEvent<float> OnRESTCalibrated;
        public UnityEvent OnCalibrationStarted;
        public UnityEvent OnCalibrationCompleted;

        void Start()
        {
            InitializeCalibration();
        }

        void Update()
        {
            if (isCalibrationActive && !isCalibrationComplete)
            {
                UpdateStabilityCheck();
                UpdateUI();
            }
        }

        void OnEnable()
        {
            // 订阅BLE数据事件
            if (BLEDeviceManager.Instance != null)
            {
                BLEDeviceManager.Instance.OnForceDataReceived.AddListener(OnForceDataReceived);
            }
        }

        void OnDisable()
        {
            // 取消订阅BLE数据事件
            if (BLEDeviceManager.Instance != null)
            {
                BLEDeviceManager.Instance.OnForceDataReceived.RemoveListener(OnForceDataReceived);
            }
        }

        /// <summary>
        /// 初始化校准系统
        /// </summary>
        private void InitializeCalibration()
        {
            isCalibrationComplete = false;
            isCalibrationActive = false;
            stabilityTimer = 0f;
            recentForceValues.Clear();

            if (startButton != null)
            {
                startButton.onClick.AddListener(StartRESTCalibration);
            }

            ShowInstructions();
        }

        /// <summary>
        /// 显示说明
        /// </summary>
        private void ShowInstructions()
        {
            if (instructionText != null)
            {
                instructionText.text = "静息状态校准\n\n" +
                    "请完全放松你的手\n" +
                    "让握力回到自然状态\n" +
                    "保持稳定5秒钟";
                instructionText.color = normalColor;
            }

            if (stabilityBar != null)
                stabilityBar.value = 0f;

            if (stabilityIndicator != null)
                stabilityIndicator.color = unstableColor;

            if (startButton != null)
                startButton.gameObject.SetActive(true);
        }

        /// <summary>
        /// 开始REST校准
        /// </summary>
        public void StartRESTCalibration()
        {
            isCalibrationActive = true;
            isCalibrationComplete = false;
            stabilityTimer = 0f;
            recentForceValues.Clear();

            if (startButton != null)
                startButton.gameObject.SetActive(false);

            if (instructionText != null)
            {
                instructionText.text = "请放松手部，保持自然状态";
                instructionText.color = normalColor;
            }

            OnCalibrationStarted?.Invoke();
            
            Debug.Log("[RESTCalibrationGame] 开始REST校准");
        }

        /// <summary>
        /// 处理力量数据
        /// </summary>
        public void OnForceDataReceived(float forceValue)
        {
            if (!isCalibrationActive) return;

            // 记录最近的力量值
            recentForceValues.Enqueue(forceValue);
            if (recentForceValues.Count > STABILITY_SAMPLE_SIZE)
            {
                recentForceValues.Dequeue();
            }
        }

        /// <summary>
        /// 更新稳定性检查
        /// </summary>
        private void UpdateStabilityCheck()
        {
            // 检查稳定性
            if (recentForceValues.Count >= STABILITY_SAMPLE_SIZE)
            {
                bool isStable = CheckStability();

                if (isStable)
                {
                    stabilityTimer += Time.deltaTime;

                    // 触发放松特效
                    if (relaxationEffect != null && !relaxationEffect.isPlaying)
                        relaxationEffect.Play();

                    // 播放稳定音效
                    if (audioSource != null && stabilitySound != null && 
                        Time.frameCount % 120 == 0) // 每2秒播放一次
                    {
                        audioSource.PlayOneShot(stabilitySound);
                    }
                }
                else
                {
                    stabilityTimer = 0f;
                    if (relaxationEffect != null)
                        relaxationEffect.Stop();
                }

                // 检查是否完成校准
                if (stabilityTimer >= targetStabilityTime)
                {
                    CompleteRESTCalibration();
                }
            }
        }

        /// <summary>
        /// 检查稳定性
        /// </summary>
        private bool CheckStability()
        {
            if (recentForceValues.Count < STABILITY_SAMPLE_SIZE)
                return false;

            float[] values = recentForceValues.ToArray();
            float mean = values.Average();
            float variance = values.Sum(v => Mathf.Pow(v - mean, 2)) / values.Length;
            float stdDev = Mathf.Sqrt(variance);

            return stdDev <= stabilityThreshold;
        }

        /// <summary>
        /// 更新UI
        /// </summary>
        private void UpdateUI()
        {
            float currentForce = BLEDeviceManager.Instance != null ? 
                BLEDeviceManager.Instance.GetCurrentForce() : 0f;
            
            if (forceValueText != null)
                forceValueText.text = $"当前值: {currentForce:F2}";

            // 更新稳定性进度条
            if (stabilityBar != null)
                stabilityBar.value = stabilityTimer / targetStabilityTime;

            // 更新稳定性指示器颜色
            bool isStable = CheckStability();
            if (stabilityIndicator != null)
            {
                Color targetColor = isStable ? stableColor : unstableColor;
                stabilityIndicator.color = Color.Lerp(stabilityIndicator.color, targetColor, Time.deltaTime * 5f);
            }

            // 更新指导文字
            if (instructionText != null)
            {
                if (stabilityTimer > 0)
                {
                    instructionText.text = $"很好! 保持稳定... ({stabilityTimer:F1}/{targetStabilityTime:F1}s)";
                    instructionText.color = stableColor;
                }
                else
                {
                    instructionText.text = "请放松手部，保持自然状态";
                    instructionText.color = normalColor;
                }
            }
        }

        /// <summary>
        /// 完成REST校准
        /// </summary>
        private void CompleteRESTCalibration()
        {
            isCalibrationComplete = true;
            isCalibrationActive = false;

            // 计算REST基线值
            currentRESTValue = recentForceValues.Average();

            if (instructionText != null)
            {
                instructionText.text = $"REST校准完成!\n基线值: {currentRESTValue:F2}";
                instructionText.color = completedColor;
            }

            if (audioSource != null && completionSound != null)
                audioSource.PlayOneShot(completionSound);

            // 触发完成事件
            OnRESTCalibrated?.Invoke(currentRESTValue);
            OnCalibrationCompleted?.Invoke();

            // 保存REST值
            if (DataManager.Instance != null)
            {
                DataManager.Instance.SetRESTValue(currentRESTValue);
            }

            Debug.Log($"[RESTCalibrationGame] REST校准完成，基线值: {currentRESTValue:F2}");

            // 2秒后开始游戏
            Invoke(nameof(StartGame), 2f);
        }

        /// <summary>
        /// 开始游戏
        /// </summary>
        private void StartGame()
        {
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.TransitionToState(GameStateManager.GameState.GameLevels);
            }
        }

        /// <summary>
        /// 获取当前REST值
        /// </summary>
        public float GetCurrentRESTValue()
        {
            return currentRESTValue;
        }

        /// <summary>
        /// 检查校准是否完成
        /// </summary>
        public bool IsCalibrationComplete()
        {
            return isCalibrationComplete;
        }

        /// <summary>
        /// 获取稳定性进度
        /// </summary>
        public float GetStabilityProgress()
        {
            return stabilityTimer / targetStabilityTime;
        }

        /// <summary>
        /// 重置校准
        /// </summary>
        public void ResetCalibration()
        {
            isCalibrationComplete = false;
            isCalibrationActive = false;
            stabilityTimer = 0f;
            recentForceValues.Clear();
            currentRESTValue = 0f;

            ShowInstructions();

            Debug.Log("[RESTCalibrationGame] 校准已重置");
        }

        /// <summary>
        /// 强制完成校准（调试用）
        /// </summary>
        [ContextMenu("Force Complete Calibration")]
        public void ForceCompleteCalibration()
        {
            // 模拟稳定的REST值
            for (int i = 0; i < STABILITY_SAMPLE_SIZE; i++)
            {
                recentForceValues.Enqueue(2f + Random.Range(-0.5f, 0.5f));
            }
            
            CompleteRESTCalibration();
        }

        /// <summary>
        /// 设置模拟REST值（测试用）
        /// </summary>
        public void SetSimulatedREST(float restValue)
        {
            currentRESTValue = restValue;
            
            Debug.Log($"[RESTCalibrationGame] 设置模拟REST值: {restValue}");
        }

        /// <summary>
        /// 获取校准统计信息
        /// </summary>
        public string GetCalibrationStats()
        {
            return $"REST值: {currentRESTValue:F2}, " +
                   $"稳定性: {GetStabilityProgress() * 100f:F1}%, " +
                   $"样本数: {recentForceValues.Count}/{STABILITY_SAMPLE_SIZE}";
        }

        // 调试方法
        [ContextMenu("Print Calibration Stats")]
        public void DebugPrintStats()
        {
            Debug.Log($"[RESTCalibrationGame] {GetCalibrationStats()}");
        }

        [ContextMenu("Reset Calibration")]
        public void DebugResetCalibration()
        {
            ResetCalibration();
        }
    }
}
