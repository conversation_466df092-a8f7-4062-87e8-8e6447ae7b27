using System.Collections.Generic;
using UnityEngine;
using ForceFollowingGame.Core;

namespace ForceFollowingGame.Game
{
    /// <summary>
    /// 轨迹生成器 - 为不同类型的关卡生成目标轨迹曲线
    /// </summary>
    public static class TrajectoryGenerator
    {
        /// <summary>
        /// 生成目标轨迹曲线
        /// </summary>
        /// <param name="level">关卡配置</param>
        /// <returns>生成的轨迹曲线</returns>
        public static AnimationCurve GenerateTargetCurve(GameLevel level)
        {
            if (level == null)
            {
                Debug.LogError("[TrajectoryGenerator] 关卡配置为空");
                return AnimationCurve.Linear(0, 0, 1, 0);
            }

            float duration = level.duration;
            float baseMVC = level.baseMVCPercent;
            float difficulty = level.difficulty;

            switch (level.trajectory)
            {
                case TrajectoryType.SteadyHold:
                    return GenerateSteadyHold(baseMVC, duration);

                case TrajectoryType.SlowRamp:
                    return GenerateSlowRamp(baseMVC, duration, difficulty);

                case TrajectoryType.FastRamp:
                    return GenerateFastRamp(baseMVC, duration, difficulty);

                case TrajectoryType.Wave:
                    return GenerateWave(baseMVC, duration, difficulty);

                case TrajectoryType.Steps:
                    return GenerateSteps(baseMVC, duration, difficulty);

                case TrajectoryType.Random:
                    return GenerateRandom(baseMVC, duration, difficulty);

                case TrajectoryType.Challenge:
                    return GenerateChallenge(baseMVC, duration, difficulty);

                default:
                    Debug.LogWarning($"[TrajectoryGenerator] 未知轨迹类型: {level.trajectory}");
                    return GenerateSteadyHold(baseMVC, duration);
            }
        }

        /// <summary>
        /// 生成自适应轨迹曲线
        /// </summary>
        public static AnimationCurve GenerateAdaptiveTargetCurve(GameLevel level)
        {
            // 为自适应关卡添加更多随机性
            var baseCurve = GenerateTargetCurve(level);
            
            // 根据难度添加噪声
            if (level.difficulty > 0.5f)
            {
                baseCurve = AddNoiseToTrajectory(baseCurve, level.difficulty * 0.1f);
            }

            return baseCurve;
        }

        /// <summary>
        /// 生成稳定保持轨迹
        /// </summary>
        private static AnimationCurve GenerateSteadyHold(float targetMVC, float duration)
        {
            var keyframes = new List<Keyframe>
            {
                new Keyframe(0f, 0f),                    // 开始为0
                new Keyframe(2f, 0f),                    // 准备期
                new Keyframe(4f, targetMVC),             // 上升到目标
                new Keyframe(duration - 4f, targetMVC), // 保持目标
                new Keyframe(duration - 2f, 0f),        // 下降
                new Keyframe(duration, 0f)              // 结束
            };

            return new AnimationCurve(keyframes.ToArray());
        }

        /// <summary>
        /// 生成缓慢上升轨迹
        /// </summary>
        private static AnimationCurve GenerateSlowRamp(float baseMVC, float duration, float difficulty)
        {
            var keyframes = new List<Keyframe>();
            
            keyframes.Add(new Keyframe(0f, 0f)); // 开始
            keyframes.Add(new Keyframe(2f, 0f)); // 准备期

            float rampStart = 2f;
            float rampEnd = duration - 4f;
            float maxHeight = baseMVC * (1f + difficulty * 0.5f);

            // 缓慢上升
            keyframes.Add(new Keyframe(rampStart, baseMVC * 0.2f));
            keyframes.Add(new Keyframe((rampStart + rampEnd) * 0.5f, maxHeight * 0.6f));
            keyframes.Add(new Keyframe(rampEnd, maxHeight));

            keyframes.Add(new Keyframe(duration - 2f, 0f)); // 结束准备
            keyframes.Add(new Keyframe(duration, 0f));      // 结束

            return new AnimationCurve(keyframes.ToArray());
        }

        /// <summary>
        /// 生成快速上升轨迹
        /// </summary>
        private static AnimationCurve GenerateFastRamp(float baseMVC, float duration, float difficulty)
        {
            var keyframes = new List<Keyframe>();
            
            keyframes.Add(new Keyframe(0f, 0f));
            keyframes.Add(new Keyframe(2f, 0f));

            float rampStart = 2f;
            float rampDuration = 5f + difficulty * 5f; // 快速上升持续时间
            float maxHeight = baseMVC * (1.2f + difficulty * 0.3f);

            // 快速上升
            keyframes.Add(new Keyframe(rampStart + 1f, maxHeight * 0.8f));
            keyframes.Add(new Keyframe(rampStart + rampDuration, maxHeight));
            keyframes.Add(new Keyframe(duration - 4f, maxHeight * 0.9f));

            keyframes.Add(new Keyframe(duration - 2f, 0f));
            keyframes.Add(new Keyframe(duration, 0f));

            return new AnimationCurve(keyframes.ToArray());
        }

        /// <summary>
        /// 生成波浪轨迹
        /// </summary>
        private static AnimationCurve GenerateWave(float baseMVC, float duration, float difficulty)
        {
            var keyframes = new List<Keyframe>();
            int numWaves = Mathf.RoundToInt(1 + difficulty * 4); // 1-5个波
            float amplitude = baseMVC * (0.5f + difficulty * 0.5f); // 振幅随难度增加

            keyframes.Add(new Keyframe(0f, 0f)); // 开始
            keyframes.Add(new Keyframe(2f, 0f)); // 准备期

            float waveStart = 2f;
            float waveEnd = duration - 4f;
            float waveDuration = waveEnd - waveStart;

            for (int i = 0; i < numWaves; i++)
            {
                float wavePhaseStart = waveStart + (waveDuration * i / numWaves);
                float wavePhaseEnd = waveStart + (waveDuration * (i + 1) / numWaves);
                float waveMid = (wavePhaseStart + wavePhaseEnd) / 2f;

                keyframes.Add(new Keyframe(wavePhaseStart, baseMVC - amplitude / 2));
                keyframes.Add(new Keyframe(waveMid, baseMVC + amplitude / 2));
                keyframes.Add(new Keyframe(wavePhaseEnd, baseMVC - amplitude / 2));
            }

            keyframes.Add(new Keyframe(duration - 2f, 0f)); // 结束准备
            keyframes.Add(new Keyframe(duration, 0f));      // 结束

            return new AnimationCurve(keyframes.ToArray());
        }

        /// <summary>
        /// 生成阶梯轨迹
        /// </summary>
        private static AnimationCurve GenerateSteps(float baseMVC, float duration, float difficulty)
        {
            var keyframes = new List<Keyframe>();
            int numSteps = Mathf.RoundToInt(2 + difficulty * 4); // 2-6个台阶

            keyframes.Add(new Keyframe(0f, 0f));
            keyframes.Add(new Keyframe(2f, 0f));

            float stepStart = 2f;
            float stepEnd = duration - 4f;
            float stepDuration = (stepEnd - stepStart) / numSteps;

            for (int i = 0; i < numSteps; i++)
            {
                float stepTime = stepStart + (stepDuration * i);
                float nextStepTime = stepStart + (stepDuration * (i + 1));
                float stepHeight = baseMVC * (0.3f + 0.7f * (i + 1) / numSteps);

                keyframes.Add(new Keyframe(stepTime, stepHeight));
                keyframes.Add(new Keyframe(nextStepTime - 0.5f, stepHeight));
            }

            keyframes.Add(new Keyframe(duration - 2f, 0f));
            keyframes.Add(new Keyframe(duration, 0f));

            return new AnimationCurve(keyframes.ToArray());
        }

        /// <summary>
        /// 生成随机轨迹
        /// </summary>
        private static AnimationCurve GenerateRandom(float baseMVC, float duration, float difficulty)
        {
            var keyframes = new List<Keyframe>();
            int numPoints = Mathf.RoundToInt(5 + difficulty * 10); // 5-15个随机点

            keyframes.Add(new Keyframe(0f, 0f));
            keyframes.Add(new Keyframe(2f, 0f));

            float randomStart = 2f;
            float randomEnd = duration - 4f;
            float randomDuration = randomEnd - randomStart;

            for (int i = 0; i < numPoints; i++)
            {
                float time = randomStart + (randomDuration * i / (numPoints - 1));
                float height = Random.Range(baseMVC * 0.2f, baseMVC * (1f + difficulty));
                
                keyframes.Add(new Keyframe(time, height));
            }

            keyframes.Add(new Keyframe(duration - 2f, 0f));
            keyframes.Add(new Keyframe(duration, 0f));

            return new AnimationCurve(keyframes.ToArray());
        }

        /// <summary>
        /// 生成挑战轨迹（混合多种类型）
        /// </summary>
        private static AnimationCurve GenerateChallenge(float baseMVC, float duration, float difficulty)
        {
            var keyframes = new List<Keyframe>();

            keyframes.Add(new Keyframe(0f, 0f));
            keyframes.Add(new Keyframe(2f, 0f));

            float challengeStart = 2f;
            float challengeEnd = duration - 4f;
            float challengeDuration = challengeEnd - challengeStart;

            // 分段生成不同类型的轨迹
            int numSegments = Mathf.RoundToInt(2 + difficulty * 3); // 2-5个段落
            float segmentDuration = challengeDuration / numSegments;

            for (int i = 0; i < numSegments; i++)
            {
                float segmentStart = challengeStart + (segmentDuration * i);
                float segmentEnd = challengeStart + (segmentDuration * (i + 1));
                
                // 随机选择段落类型
                TrajectoryType segmentType = (TrajectoryType)Random.Range(0, 4);
                AddSegmentToKeyframes(keyframes, segmentType, segmentStart, segmentEnd, 
                    baseMVC, difficulty);
            }

            keyframes.Add(new Keyframe(duration - 2f, 0f));
            keyframes.Add(new Keyframe(duration, 0f));

            return new AnimationCurve(keyframes.ToArray());
        }

        /// <summary>
        /// 添加段落到关键帧列表
        /// </summary>
        private static void AddSegmentToKeyframes(List<Keyframe> keyframes, TrajectoryType type,
            float startTime, float endTime, float baseMVC, float difficulty)
        {
            float segmentDuration = endTime - startTime;
            float midTime = (startTime + endTime) / 2f;

            switch (type)
            {
                case TrajectoryType.SteadyHold:
                    float holdHeight = baseMVC * Random.Range(0.5f, 1f + difficulty);
                    keyframes.Add(new Keyframe(startTime, holdHeight));
                    keyframes.Add(new Keyframe(endTime, holdHeight));
                    break;

                case TrajectoryType.SlowRamp:
                    float rampStart = baseMVC * Random.Range(0.2f, 0.5f);
                    float rampEnd = baseMVC * Random.Range(0.8f, 1.2f + difficulty);
                    keyframes.Add(new Keyframe(startTime, rampStart));
                    keyframes.Add(new Keyframe(endTime, rampEnd));
                    break;

                case TrajectoryType.Wave:
                    float waveBase = baseMVC * Random.Range(0.4f, 0.8f);
                    float waveAmp = baseMVC * Random.Range(0.2f, 0.4f * difficulty);
                    keyframes.Add(new Keyframe(startTime, waveBase - waveAmp));
                    keyframes.Add(new Keyframe(midTime, waveBase + waveAmp));
                    keyframes.Add(new Keyframe(endTime, waveBase - waveAmp));
                    break;

                case TrajectoryType.Steps:
                    float step1 = baseMVC * Random.Range(0.3f, 0.7f);
                    float step2 = baseMVC * Random.Range(0.6f, 1f + difficulty * 0.5f);
                    keyframes.Add(new Keyframe(startTime, step1));
                    keyframes.Add(new Keyframe(midTime, step1));
                    keyframes.Add(new Keyframe(midTime + 0.1f, step2));
                    keyframes.Add(new Keyframe(endTime, step2));
                    break;
            }
        }

        /// <summary>
        /// 为轨迹添加噪声
        /// </summary>
        private static AnimationCurve AddNoiseToTrajectory(AnimationCurve baseCurve, float noiseAmount)
        {
            var keys = baseCurve.keys;
            
            for (int i = 1; i < keys.Length - 1; i++) // 跳过首尾关键帧
            {
                float noise = Random.Range(-noiseAmount, noiseAmount);
                keys[i].value = Mathf.Max(0, keys[i].value + noise);
            }

            return new AnimationCurve(keys);
        }

        /// <summary>
        /// 平滑轨迹曲线
        /// </summary>
        public static AnimationCurve SmoothTrajectory(AnimationCurve curve, float smoothness = 0.5f)
        {
            var keys = curve.keys;
            
            // 设置切线以创建平滑过渡
            for (int i = 0; i < keys.Length; i++)
            {
                keys[i].inTangent = smoothness;
                keys[i].outTangent = smoothness;
            }

            return new AnimationCurve(keys);
        }

        /// <summary>
        /// 验证轨迹曲线的有效性
        /// </summary>
        public static bool ValidateTrajectory(AnimationCurve curve, float maxValue = 100f)
        {
            if (curve == null || curve.keys.Length < 2)
                return false;

            foreach (var key in curve.keys)
            {
                if (key.value < 0 || key.value > maxValue)
                    return false;
            }

            return true;
        }
    }
}
