# 游戏化力量跟随系统设计文档

## 1. 系统概述

### 1.1 项目目标
将传统的力量跟随实验转化为类似Flappy Bird的游戏化体验，通过BLE握力计采集数据，让被试者在游戏中完成力量控制任务，提高参与度和数据质量。

### 1.2 核心理念
- **游戏化设计**：关卡制、评分系统、自适应难度
- **用户友好**：被试者自主操作，直观的视觉反馈
- **科学严谨**：保持实验数据的有效性和可靠性
- **跨平台支持**：Android平板和Windows PC

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Unity游戏引擎层                           │
├─────────────────────────────────────────────────────────────┤
│  用户界面层  │  游戏逻辑层  │  数据分析层  │  设备管理层    │
│  (Game UI)   │ (Game Logic) │ (Analytics)  │ (Device Mgr)   │
├─────────────────────────────────────────────────────────────┤
│            蓝牙通信层 (BLE Communication)                    │
├─────────────────────────────────────────────────────────────┤
│              握力计设备 (BLE Grip Sensor)                   │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心模块设计

#### 游戏状态管理器
```csharp
public class GameStateManager : MonoBehaviour
{
    public enum GameState
    {
        MainMenu,           // 主菜单（用户登录）
        DeviceConnection,   // 设备连接
        Tutorial,           // 教程
        MVCCalibration,     // MVC校准游戏
        RESTCalibration,    // REST校准游戏
        GameLevels,         // 游戏关卡
        LevelTransition,    // 关卡间休息
        Results,            // 最终结果
        Settings            // 设置界面
    }
    
    [Header("状态管理")]
    public GameState currentState;
    public UnityEvent<GameState> OnStateChanged;
    
    public void TransitionToState(GameState newState)
    {
        // 状态转换逻辑和验证
        if (ValidateStateTransition(currentState, newState))
        {
            ExitCurrentState();
            currentState = newState;
            EnterNewState();
            OnStateChanged?.Invoke(newState);
        }
    }
}
```

#### 蓝牙设备管理器
```csharp
public class BLEDeviceManager : MonoBehaviour
{
    [Header("设备配置")]
    public string targetDeviceMAC = "3C:AB:72:6F:68:6D";
    public string notifyCharUUID = "9ECADC24-0EE5-A9E0-93F3-A3B50300406E";
    
    [Header("连接状态")]
    public bool isConnected = false;
    public UnityEvent OnDeviceConnected;
    public UnityEvent OnDeviceDisconnected;
    public UnityEvent<float> OnForceDataReceived;
    
    private Queue<byte> dataBuffer = new Queue<byte>();
    private const int PACKET_SIZE = 6;
    private const byte HEADER_1 = 0x40;
    private const byte HEADER_2 = 0x5C;
    
    public async Task<bool> ConnectToDevice()
    {
        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            return await ConnectAndroid();
#elif UNITY_STANDALONE_WIN
            return await ConnectWindows();
#else
            return SimulateConnection(); // Editor模式下模拟连接
#endif
        }
        catch (Exception e)
        {
            Debug.LogError($"设备连接失败: {e.Message}");
            return false;
        }
    }
    
    private void ProcessBLEData(byte[] newData)
    {
        // 数据包解析（参照Python实现）
        foreach (byte b in newData)
            dataBuffer.Enqueue(b);
            
        while (dataBuffer.Count >= PACKET_SIZE)
        {
            if (FindAndValidatePacket(out float pressure))
            {
                OnForceDataReceived?.Invoke(pressure);
            }
        }
    }
    
    private bool FindAndValidatePacket(out float pressure)
    {
        pressure = 0f;
        
        // 查找数据包头
        while (dataBuffer.Count >= PACKET_SIZE)
        {
            byte[] packet = new byte[PACKET_SIZE];
            for (int i = 0; i < PACKET_SIZE; i++)
                packet[i] = dataBuffer.Dequeue();
                
            if (packet[0] == HEADER_1 && packet[1] == HEADER_2)
            {
                // 解析24位压力值
                uint pressureRaw = ((uint)packet[2] << 16) | 
                                  ((uint)packet[3] << 8) | 
                                  (uint)packet[4];
                
                // 校验和验证
                byte checksum = (byte)((packet[0] + packet[1] + packet[2] + packet[3] + packet[4]) & 0xFF);
                if (checksum == packet[5])
                {
                    pressure = ConvertRawToPressure(pressureRaw);
                    return true;
                }
            }
        }
        return false;
    }
    
    private float ConvertRawToPressure(uint rawValue)
    {
        // 将24位原始值转换为标准化压力值
        // 假设最大值16777215对应100%
        return (float)rawValue / 16777215f * 100f;
    }
}
```

## 3. 游戏机制设计

### 3.1 关卡系统
```csharp
public class LevelManager : MonoBehaviour
{
    [System.Serializable]
    public class GameLevel
    {
        public int levelNumber;
        public string levelName;
        public string description;
        public float baseMVCPercent;      // 基础MVC百分比
        public TrajectoryType trajectory; // 轨迹类型
        public float duration;            // 关卡时长(秒)
        public float difficulty;          // 难度系数(0-1)
        public AnimationCurve targetCurve; // 目标轨迹曲线
    }
    
    public enum TrajectoryType
    {
        SteadyHold,     // 稳定保持
        SlowRamp,       // 缓慢上升
        FastRamp,       // 快速上升  
        Wave,           // 正弦波
        Steps,          // 阶梯状
        Random,         // 随机波动
        Challenge       // 自适应挑战
    }
    
    [Header("关卡配置")]
    public List<GameLevel> predefinedLevels;
    public int currentLevelIndex = 0;
    public GameLevel currentLevel;
    
    [Header("自适应系统")]
    public AdaptiveDifficultySystem adaptiveSystem;
    
    void Start()
    {
        InitializePredefinedLevels();
        currentLevel = predefinedLevels[0];
    }
    
    private void InitializePredefinedLevels()
    {
        predefinedLevels = new List<GameLevel>
        {
            new GameLevel 
            { 
                levelNumber = 1, 
                levelName = "热身运动", 
                description = "保持轻微握力，适应游戏",
                baseMVCPercent = 5f, 
                trajectory = TrajectoryType.SteadyHold,
                duration = 20f,
                difficulty = 0.1f
            },
            new GameLevel 
            { 
                levelNumber = 2, 
                levelName = "缓慢爬升", 
                description = "跟随目标缓慢增加握力",
                baseMVCPercent = 10f, 
                trajectory = TrajectoryType.SlowRamp,
                duration = 30f,
                difficulty = 0.3f
            },
            new GameLevel 
            { 
                levelNumber = 3, 
                levelName = "波浪起伏", 
                description = "跟随波浪形轨迹变化",
                baseMVCPercent = 15f, 
                trajectory = TrajectoryType.Wave,
                duration = 35f,
                difficulty = 0.5f
            },
            new GameLevel 
            { 
                levelNumber = 4, 
                levelName = "阶梯挑战", 
                description = "快速适应不同握力水平",
                baseMVCPercent = 20f, 
                trajectory = TrajectoryType.Steps,
                duration = 40f,
                difficulty = 0.7f
            },
            new GameLevel 
            { 
                levelNumber = 5, 
                levelName = "终极挑战", 
                description = "复杂轨迹的终极考验",
                baseMVCPercent = 25f, 
                trajectory = TrajectoryType.Challenge,
                duration = 45f,
                difficulty = 0.9f
            }
        };
        
        // 为每个关卡生成目标曲线
        foreach (var level in predefinedLevels)
        {
            level.targetCurve = GenerateTargetCurve(level);
        }
    }
    
    public GameLevel GetNextLevel()
    {
        currentLevelIndex++;
        
        if (currentLevelIndex < predefinedLevels.Count)
        {
            currentLevel = predefinedLevels[currentLevelIndex];
        }
        else
        {
            // 生成自适应关卡
            currentLevel = adaptiveSystem.GenerateAdaptiveLevel(currentLevelIndex);
        }
        
        return currentLevel;
    }
}
```

### 3.2 自适应难度系统
```csharp
public class AdaptiveDifficultySystem : MonoBehaviour
{
    [Header("性能历史")]
    public Queue<float> recentScores = new Queue<float>();
    public int maxHistorySize = 5;
    
    [Header("难度参数")]
    public float baseDifficulty = 0.5f;
    public float difficultyAdjustmentRate = 0.1f;
    public float minDifficulty = 0.1f;
    public float maxDifficulty = 1.0f;
    
    public void RecordPerformance(float score)
    {
        recentScores.Enqueue(score);
        if (recentScores.Count > maxHistorySize)
            recentScores.Dequeue();
    }
    
    public GameLevel GenerateAdaptiveLevel(int levelNumber)
    {
        float avgPerformance = CalculateAveragePerformance();
        float adaptedDifficulty = AdaptDifficulty(avgPerformance);
        
        var adaptiveLevel = new GameLevel
        {
            levelNumber = levelNumber,
            levelName = $"自适应关卡 {levelNumber - 4}",
            description = GetAdaptiveDescription(adaptedDifficulty),
            baseMVCPercent = CalculateAdaptiveMVC(adaptedDifficulty),
            trajectory = SelectAdaptiveTrajectory(adaptedDifficulty),
            duration = 30f + adaptedDifficulty * 20f,
            difficulty = adaptedDifficulty
        };
        
        adaptiveLevel.targetCurve = GenerateAdaptiveTargetCurve(adaptiveLevel);
        return adaptiveLevel;
    }
    
    private float CalculateAveragePerformance()
    {
        if (recentScores.Count == 0) return 0.75f; // 默认中等水平
        return recentScores.Average();
    }
    
    private float AdaptDifficulty(float avgPerformance)
    {
        float targetPerformance = 0.75f; // 目标75%正确率
        float performanceDiff = avgPerformance - targetPerformance;
        
        // 表现好 -> 增加难度，表现差 -> 降低难度
        float newDifficulty = baseDifficulty - (performanceDiff * difficultyAdjustmentRate);
        return Mathf.Clamp(newDifficulty, minDifficulty, maxDifficulty);
    }
    
    private string GetAdaptiveDescription(float difficulty)
    {
        if (difficulty < 0.3f) return "轻松模式 - 稳定跟随";
        if (difficulty < 0.6f) return "标准模式 - 适中挑战";
        if (difficulty < 0.8f) return "困难模式 - 复杂轨迹";
        return "地狱模式 - 极限挑战";
    }
    
    private TrajectoryType SelectAdaptiveTrajectory(float difficulty)
    {
        if (difficulty < 0.2f) return TrajectoryType.SteadyHold;
        if (difficulty < 0.4f) return TrajectoryType.SlowRamp;
        if (difficulty < 0.6f) return TrajectoryType.Wave;
        if (difficulty < 0.8f) return TrajectoryType.Steps;
        return TrajectoryType.Random;
    }
}
```

### 3.3 轨迹生成系统
```csharp
public class TrajectoryGenerator : MonoBehaviour
{
    public static AnimationCurve GenerateTargetCurve(GameLevel level)
    {
        var keyframes = new List<Keyframe>();
        float duration = level.duration;
        float baseMVC = level.baseMVCPercent;
        float difficulty = level.difficulty;
        
        switch (level.trajectory)
        {
            case TrajectoryType.SteadyHold:
                return GenerateSteadyHold(baseMVC, duration);
                
            case TrajectoryType.SlowRamp:
                return GenerateSlowRamp(baseMVC, duration, difficulty);
                
            case TrajectoryType.Wave:
                return GenerateWave(baseMVC, duration, difficulty);
                
            case TrajectoryType.Steps:
                return GenerateSteps(baseMVC, duration, difficulty);
                
            case TrajectoryType.Random:
                return GenerateRandom(baseMVC, duration, difficulty);
                
            case TrajectoryType.Challenge:
                return GenerateChallenge(baseMVC, duration, difficulty);
                
            default:
                return GenerateSteadyHold(baseMVC, duration);
        }
    }
    
    private static AnimationCurve GenerateSteadyHold(float targetMVC, float duration)
    {
        return new AnimationCurve(
            new Keyframe(0f, 0f),                    // 开始为0
            new Keyframe(2f, 0f),                    // 准备期
            new Keyframe(4f, targetMVC),             // 上升到目标
            new Keyframe(duration - 4f, targetMVC), // 保持目标
            new Keyframe(duration - 2f, 0f),        // 下降
            new Keyframe(duration, 0f)              // 结束
        );
    }
    
    private static AnimationCurve GenerateWave(float baseMVC, float duration, float difficulty)
    {
        var keyframes = new List<Keyframe>();
        int numWaves = Mathf.RoundToInt(1 + difficulty * 4); // 1-5个波
        float amplitude = baseMVC * (0.5f + difficulty * 0.5f); // 振幅随难度增加
        
        keyframes.Add(new Keyframe(0f, 0f)); // 开始
        keyframes.Add(new Keyframe(2f, 0f)); // 准备期
        
        for (int i = 0; i < numWaves; i++)
        {
            float waveStart = 2f + (duration - 6f) * i / numWaves;
            float waveEnd = 2f + (duration - 6f) * (i + 1) / numWaves;
            float waveMid = (waveStart + waveEnd) / 2f;
            
            keyframes.Add(new Keyframe(waveStart, baseMVC - amplitude/2));
            keyframes.Add(new Keyframe(waveMid, baseMVC + amplitude/2));
            keyframes.Add(new Keyframe(waveEnd, baseMVC - amplitude/2));
        }
        
        keyframes.Add(new Keyframe(duration - 2f, 0f)); // 结束准备
        keyframes.Add(new Keyframe(duration, 0f));      // 结束
        
        return new AnimationCurve(keyframes.ToArray());
    }
    
    private static AnimationCurve GenerateSteps(float baseMVC, float duration, float difficulty)
    {
        var keyframes = new List<Keyframe>();
        int numSteps = Mathf.RoundToInt(2 + difficulty * 4); // 2-6个台阶
        
        keyframes.Add(new Keyframe(0f, 0f));
        keyframes.Add(new Keyframe(2f, 0f));
        
        for (int i = 0; i < numSteps; i++)
        {
            float stepStart = 2f + (duration - 6f) * i / numSteps;
            float stepEnd = 2f + (duration - 6f) * (i + 1) / numSteps;
            float stepHeight = baseMVC * (0.3f + 0.7f * (i + 1) / numSteps);
            
            keyframes.Add(new Keyframe(stepStart, stepHeight));
            keyframes.Add(new Keyframe(stepEnd - 0.5f, stepHeight));
        }
        
        keyframes.Add(new Keyframe(duration - 2f, 0f));
        keyframes.Add(new Keyframe(duration, 0f));
        
        return new AnimationCurve(keyframes.ToArray());
    }
}
```

## 4. 游戏界面设计

### 4.1 主游戏界面
```csharp
public class GameplayUI : MonoBehaviour
{
    [Header("游戏视觉元素")]
    public RectTransform gameArea;           // 游戏区域
    public LineRenderer targetPath;          // 目标轨迹线
    public Transform playerBird;             // 玩家"小鸟"
    public ParticleSystem successEffect;     // 成功特效
    public ParticleSystem trailEffect;       // 轨迹特效
    
    [Header("UI元素")]
    public Text levelNameText;               // 关卡名称
    public Text scoreText;                   // 当前得分
    public Slider progressBar;               // 进度条
    public Slider forceBar;                  // 当前力量条
    public Text timeRemainingText;           // 剩余时间
    
    [Header("视觉反馈")]
    public Image accuracyIndicator;          // 精度指示器
    public Color perfectColor = Color.green; // 完美跟随颜色
    public Color goodColor = Color.yellow;   // 良好跟随颜色
    public Color poorColor = Color.red;      // 差劲跟随颜色
    
    [Header("游戏状态")]
    public float currentForce = 0f;
    public float targetForce = 0f;
    public float currentScore = 0f;
    public float gameTime = 0f;
    public float totalDuration = 30f;
    
    private Camera gameCamera;
    private Vector3 lastBirdPosition;
    
    void Start()
    {
        gameCamera = Camera.main;
        InitializeUI();
    }
    
    void Update()
    {
        if (GameStateManager.Instance.currentState == GameState.GameLevels)
        {
            UpdateGameplay();
            UpdateVisuals();
            UpdateUI();
        }
    }
    
    private void UpdateGameplay()
    {
        gameTime += Time.deltaTime;
        
        // 获取当前目标力量
        float normalizedTime = gameTime / totalDuration;
        targetForce = LevelManager.Instance.currentLevel.targetCurve.Evaluate(normalizedTime);
        
        // 计算得分
        float deviation = Mathf.Abs(currentForce - targetForce);
        float frameScore = Mathf.Max(0, 100f - deviation * 2f); // 偏差越小得分越高
        currentScore += frameScore * Time.deltaTime;
    }
    
    private void UpdateVisuals()
    {
        // 更新小鸟位置
        UpdateBirdPosition();
        
        // 更新目标轨迹显示
        UpdateTargetPath();
        
        // 更新视觉反馈
        UpdateAccuracyFeedback();
    }
    
    private void UpdateBirdPosition()
    {
        // 将力量值映射到屏幕位置
        float screenHeight = gameArea.rect.height;
        float birdY = Mathf.Lerp(0.1f * screenHeight, 0.9f * screenHeight, currentForce / 100f);
        
        Vector3 newPos = new Vector3(playerBird.localPosition.x, birdY, 0);
        playerBird.localPosition = Vector3.Lerp(playerBird.localPosition, newPos, Time.deltaTime * 10f);
        
        // 小鸟旋转角度（根据力量变化）
        float forceChangeRate = (currentForce - GetPreviousForce()) / Time.deltaTime;
        float rotation = Mathf.Clamp(forceChangeRate * 2f, -30f, 30f);
        playerBird.rotation = Quaternion.Lerp(playerBird.rotation, 
            Quaternion.Euler(0, 0, rotation), Time.deltaTime * 5f);
        
        // 更新轨迹特效
        if (trailEffect != null)
        {
            var emission = trailEffect.emission;
            emission.rateOverTime = Mathf.Lerp(10f, 50f, currentForce / 100f);
        }
    }
    
    private void UpdateTargetPath()
    {
        // 动态绘制目标路径
        int pathPoints = 100;
        Vector3[] positions = new Vector3[pathPoints];
        
        for (int i = 0; i < pathPoints; i++)
        {
            float t = (float)i / (pathPoints - 1);
            float pathForce = LevelManager.Instance.currentLevel.targetCurve.Evaluate(t);
            float pathY = Mathf.Lerp(0.1f * gameArea.rect.height, 0.9f * gameArea.rect.height, pathForce / 100f);
            float pathX = Mathf.Lerp(-gameArea.rect.width/2, gameArea.rect.width/2, t);
            
            positions[i] = new Vector3(pathX, pathY, 0);
        }
        
        targetPath.positionCount = pathPoints;
        targetPath.SetPositions(positions);
        
        // 路径颜色渐变（已完成部分vs未完成部分）
        float completionRatio = gameTime / totalDuration;
        Gradient pathGradient = new Gradient();
        // 设置渐变颜色...
    }
    
    private void UpdateAccuracyFeedback()
    {
        float deviation = Mathf.Abs(currentForce - targetForce);
        Color feedbackColor;
        
        if (deviation < 5f)
            feedbackColor = perfectColor;
        else if (deviation < 15f)
            feedbackColor = goodColor;
        else
            feedbackColor = poorColor;
            
        accuracyIndicator.color = Color.Lerp(accuracyIndicator.color, feedbackColor, Time.deltaTime * 5f);
        
        // 触发特效
        if (deviation < 3f && !successEffect.isPlaying)
        {
            successEffect.Play();
        }
    }
    
    private void UpdateUI()
    {
        levelNameText.text = LevelManager.Instance.currentLevel.levelName;
        scoreText.text = $"得分: {currentScore:F0}";
        progressBar.value = gameTime / totalDuration;
        forceBar.value = currentForce / 100f;
        
        float timeRemaining = Mathf.Max(0, totalDuration - gameTime);
        timeRemainingText.text = $"剩余: {timeRemaining:F1}s";
        
        // 检查关卡是否完成
        if (gameTime >= totalDuration)
        {
            CompleteLevel();
        }
    }
    
    private void CompleteLevel()
    {
        // 计算最终得分
        var performance = PerformanceAnalyzer.Instance.AnalyzeCurrentLevel();
        
        // 转换到关卡间休息界面
        GameStateManager.Instance.TransitionToState(GameState.LevelTransition);
        LevelTransitionUI.Instance.ShowResults(performance);
    }
    
    // 供BLE设备管理器调用
    public void OnForceDataReceived(float forceValue)
    {
        currentForce = forceValue;
    }
}
```

### 4.2 关卡间休息界面
```csharp
public class LevelTransitionUI : MonoBehaviour
{
    public static LevelTransitionUI Instance;
    
    [Header("结果显示")]
    public Text levelCompleteText;           // "第X关完成!"
    public Text finalScoreText;              // "最终得分: XXX"
    public Text accuracyText;                // "跟随精度: XX%"
    public Text consistencyText;             // "稳定性: XX%"
    public Image[] starsDisplay;             // 星级显示
    
    [Header("下一关预览")]
    public Text nextLevelNameText;           // 下一关名称
    public Text nextLevelDescText;           // 下一关描述
    public Image nextLevelPreview;           // 下一关轨迹预览
    
    [Header("操作按钮")]
    public Button continueButton;            // "继续下一关"
    public Button restButton;                // "休息一会"
    public Button quitButton;                // "结束游戏"
    
    [Header("鼓励文字")]
    public Text encouragementText;           // 鼓励性文字
    public string[] perfectMessages = {"完美!", "太棒了!", "你是握力大师!"};
    public string[] goodMessages = {"很好!", "继续保持!", "不错的表现!"};
    public string[] okMessages = {"还可以!", "再接再厉!", "你在进步!"};
    public string[] poorMessages = {"加油!", "别放弃!", "下次会更好!"};
    
    void Awake()
    {
        Instance = this;
    }
    
    void Start()
    {
        continueButton.onClick.AddListener(StartNextLevel);
        restButton.onClick.AddListener(ShowRestOptions);
        quitButton.onClick.AddListener(QuitGame);
    }
    
    public void ShowResults(PerformanceMetrics performance)
    {
        StartCoroutine(AnimateResults(performance));
        PreviewNextLevel();
    }
    
    private IEnumerator AnimateResults(PerformanceMetrics performance)
    {
        // 动画显示结果
        levelCompleteText.text = $"第{LevelManager.Instance.currentLevelIndex}关完成!";
        
        // 分数动画
        yield return AnimateScore(0, performance.finalScore);
        
        // 精度动画
        yield return AnimatePercentage(accuracyText, "跟随精度", performance.accuracy);
        
        // 稳定性动画
        yield return AnimatePercentage(consistencyText, "稳定性", performance.consistency);
        
        // 星级动画
        yield return AnimateStars(performance.stars);
        
        // 鼓励文字
        ShowEncouragement(performance.stars);
        
        // 启用按钮
        EnableButtons();
    }
    
    private IEnumerator AnimateScore(float from, float to)
    {
        float duration = 1.5f;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float currentScore = Mathf.Lerp(from, to, elapsed / duration);
            finalScoreText.text = $"最终得分: {currentScore:F0}";
            yield return null;
        }
        
        finalScoreText.text = $"最终得分: {to:F0}";
    }
    
    private IEnumerator AnimatePercentage(Text targetText, string label, float percentage)
    {
        float duration = 1f;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float current = Mathf.Lerp(0, percentage, elapsed / duration);
            targetText.text = $"{label}: {current:F1}%";
            yield return null;
        }
        
        targetText.text = $"{label}: {percentage:F1}%";
    }
    
    private IEnumerator AnimateStars(int starCount)
    {
        // 先隐藏所有星星
        foreach (var star in starsDisplay)
        {
            star.color = Color.gray;
            star.transform.localScale = Vector3.zero;
        }
        
        // 逐个显示获得的星星
        for (int i = 0; i < starCount; i++)
        {
            yield return new WaitForSeconds(0.3f);
            
            var star = starsDisplay[i];
            star.color = Color.yellow;
            
            // 星星出现动画
            LeanTween.scale(star.gameObject, Vector3.one, 0.5f)
                .setEase(LeanTweenType.easeOutBounce);
            
            // 播放星星音效
            AudioManager.Instance.PlayStarSound();
        }
    }
    
    private void ShowEncouragement(int stars)
    {
        string[] messages;
        switch (stars)
        {
            case 3: messages = perfectMessages; break;
            case 2: messages = goodMessages; break;
            case 1: messages = okMessages; break;
            default: messages = poorMessages; break;
        }
        
        encouragementText.text = messages[Random.Range(0, messages.Length)];
    }
    
    private void PreviewNextLevel()
    {
        var nextLevel = LevelManager.Instance.PeekNextLevel();
        if (nextLevel != null)
        {
            nextLevelNameText.text = nextLevel.levelName;
            nextLevelDescText.text = nextLevel.description;
            
            // 生成下一关轨迹预览图
            GenerateLevelPreview(nextLevel);
        }
        else
        {
            // 所有关卡完成
            nextLevelNameText.text = "恭喜完成所有关卡!";
            nextLevelDescText.text = "你已经是真正的握力大师了!";
            continueButton.gameObject.SetActive(false);
        }
    }
    
    private void GenerateLevelPreview(GameLevel level)
    {
        // 创建简化的轨迹预览图
        // 这里可以使用UI LineRenderer或者生成贴图
    }
    
    private void EnableButtons()
    {
        continueButton.interactable = true;
        restButton.interactable = true;
        quitButton.interactable = true;
    }
    
    private void StartNextLevel()
    {
        LevelManager.Instance.LoadNextLevel();
        GameStateManager.Instance.TransitionToState(GameState.GameLevels);
    }
    
    private void ShowRestOptions()
    {
        // 显示休息选项（可以有不同的休息时长）
        RestOptionsPanel.Instance.Show();
    }
    
    private void QuitGame()
    {
        // 保存数据并退出到主菜单
        DataManager.Instance.SaveCurrentSession();
        GameStateManager.Instance.TransitionToState(GameState.Results);
    }
}
```

## 5. 性能分析系统

### 5.1 性能指标计算
```csharp
public class PerformanceAnalyzer : MonoBehaviour
{
    public static PerformanceAnalyzer Instance;
    
    [System.Serializable]
    public struct PerformanceMetrics
    {
        public float accuracy;          // 跟随精度 (0-100%)
        public float consistency;       // 一致性/稳定性 (0-100%)
        public float responseTime;      // 平均反应时间 (ms)
        public float finalScore;        // 最终得分 (0-1000+)
        public int stars;              // 星级评定 (0-3)
        public float duration;         // 完成时间
        public Dictionary<string, float> detailedMetrics; // 详细指标
    }
    
    [Header("实时数据收集")]
    public List<ForceDataPoint> currentLevelData;
    public float levelStartTime;
    public GameLevel currentLevel;
    
    [System.Serializable]
    public struct ForceDataPoint
    {
        public float timestamp;
        public float actualForce;
        public float targetForce;
        public float deviation;
        public bool isInTolerance;
    }
    
    void Awake()
    {
        Instance = this;
    }
    
    public void StartLevelAnalysis(GameLevel level)
    {
        currentLevel = level;
        currentLevelData.Clear();
        levelStartTime = Time.time;
    }
    
    public void RecordDataPoint(float actualForce, float targetForce)
    {
        var dataPoint = new ForceDataPoint
        {
            timestamp = Time.time - levelStartTime,
            actualForce = actualForce,
            targetForce = targetForce,
            deviation = Mathf.Abs(actualForce - targetForce),
            isInTolerance = Mathf.Abs(actualForce - targetForce) <= 10f // 10%容差
        };
        
        currentLevelData.Add(dataPoint);
    }
    
    public PerformanceMetrics AnalyzeCurrentLevel()
    {
        var metrics = new PerformanceMetrics();
        
        if (currentLevelData.Count == 0)
        {
            return GetDefaultMetrics();
        }
        
        // 计算精度 - 基于偏差的倒数
        float totalDeviation = currentLevelData.Sum(d => d.deviation);
        float avgDeviation = totalDeviation / currentLevelData.Count;
        metrics.accuracy = Mathf.Max(0, 100f - avgDeviation * 2f); // 偏差越小精度越高
        
        // 计算一致性 - 基于方差
        metrics.consistency = CalculateConsistency();
        
        // 计算反应时间
        metrics.responseTime = CalculateResponseTime();
        
        // 计算最终得分
        metrics.finalScore = CalculateFinalScore(metrics);
        
        // 星级评定
        metrics.stars = GetStarRating(metrics.finalScore);
        
        // 记录持续时间
        metrics.duration = currentLevelData.LastOrDefault().timestamp;
        
        // 详细指标
        metrics.detailedMetrics = CalculateDetailedMetrics();
        
        return metrics;
    }
    
    private float CalculateConsistency()
    {
        if (currentLevelData.Count < 2) return 0f;
        
        // 计算力量变化的方差（越小越稳定）
        var forceValues = currentLevelData.Select(d => d.actualForce).ToArray();
        float mean = forceValues.Average();
        float variance = forceValues.Sum(f => Mathf.Pow(f - mean, 2)) / forceValues.Length;
        float stdDev = Mathf.Sqrt(variance);
        
        // 将标准差转换为一致性分数（0-100%）
        float consistencyScore = Mathf.Max(0, 100f - stdDev * 3f);
        return consistencyScore;
    }
    
    private float CalculateResponseTime()
    {
        // 分析力量变化对目标变化的响应延迟
        var responseDelays = new List<float>();
        
        for (int i = 1; i < currentLevelData.Count - 1; i++)
        {
            var prev = currentLevelData[i - 1];
            var curr = currentLevelData[i];
            var next = currentLevelData[i + 1];
            
            // 检测目标变化
            float targetChange = Mathf.Abs(curr.targetForce - prev.targetForce);
            if (targetChange > 5f) // 目标发生显著变化
            {
                // 寻找实际力量开始响应的时间点
                for (int j = i; j < Math.Min(i + 10, currentLevelData.Count); j++)
                {
                    float actualChange = Mathf.Abs(currentLevelData[j].actualForce - curr.actualForce);
                    if (actualChange > targetChange * 0.3f) // 开始响应
                    {
                        float delay = currentLevelData[j].timestamp - curr.timestamp;
                        responseDelays.Add(delay);
                        break;
                    }
                }
            }
        }
        
        return responseDelays.Count > 0 ? responseDelays.Average() * 1000f : 0f; // 转换为毫秒
    }
    
    private float CalculateFinalScore(PerformanceMetrics metrics)
    {
        // 综合评分公式
        float accuracyWeight = 0.5f;
        float consistencyWeight = 0.3f;
        float responseWeight = 0.2f;
        
        float accuracyScore = metrics.accuracy * accuracyWeight;
        float consistencyScore = metrics.consistency * consistencyWeight;
        float responseScore = Mathf.Max(0, 100f - metrics.responseTime / 10f) * responseWeight; // 响应时间越短分越高
        
        float baseScore = accuracyScore + consistencyScore + responseScore;
        
        // 难度加成
        float difficultyBonus = currentLevel.difficulty * 200f;
        
        return baseScore * 10f + difficultyBonus; // 基础分*10 + 难度奖励
    }
    
    private int GetStarRating(float finalScore)
    {
        if (finalScore >= 900f) return 3;      // 3星：900+分
        if (finalScore >= 700f) return 2;      // 2星：700-899分
        if (finalScore >= 500f) return 1;      // 1星：500-699分
        return 0;                              // 0星：<500分
    }
    
    private Dictionary<string, float> CalculateDetailedMetrics()
    {
        var detailed = new Dictionary<string, float>();
        
        // 容差内时间百分比
        int toleranceCount = currentLevelData.Count(d => d.isInTolerance);
        detailed["TolerancePercentage"] = (float)toleranceCount / currentLevelData.Count * 100f;
        
        // 最大偏差
        detailed["MaxDeviation"] = currentLevelData.Max(d => d.deviation);
        
        // 平均偏差
        detailed["AvgDeviation"] = currentLevelData.Average(d => d.deviation);
        
        // 力量范围利用率
        float minForce = currentLevelData.Min(d => d.actualForce);
        float maxForce = currentLevelData.Max(d => d.actualForce);
        detailed["ForceRange"] = maxForce - minForce;
        
        return detailed;
    }
    
    private PerformanceMetrics GetDefaultMetrics()
    {
        return new PerformanceMetrics
        {
            accuracy = 0f,
            consistency = 0f,
            responseTime = 0f,
            finalScore = 0f,
            stars = 0,
            duration = 0f,
            detailedMetrics = new Dictionary<string, float>()
        };
    }
}
```

## 6. 校准系统设计

### 6.1 游戏化MVC测量
```csharp
public class MVCCalibrationGame : MonoBehaviour
{
    [Header("校准参数")]
    public float calibrationDuration = 15f;    // 校准持续时间
    public float warmupDuration = 3f;          // 热身时间
    public float measurementDuration = 10f;    // 实际测量时间
    public float cooldownDuration = 2f;        // 冷却时间
    
    [Header("UI元素")]
    public Text instructionText;               // 指导文字
    public Text countdownText;                 // 倒计时
    public Slider forceProgressBar;            // 力量进度条
    public Text currentForceText;              // 当前力量显示
    public Text maxForceText;                  // 最大力量显示
    public ParticleSystem encouragementEffect; // 鼓励特效
    
    [Header("音效")]
    public AudioSource audioSource;
    public AudioClip countdownBeep;
    public AudioClip startSound;
    public AudioClip newRecordSound;
    
    private float currentMVC = 0f;
    private float sessionMaxForce = 0f;
    private float calibrationTimer = 0f;
    private CalibrationPhase currentPhase;
    
    public enum CalibrationPhase
    {
        Instruction,    // 说明阶段
        Warmup,         // 热身阶段
        Measurement,    // 测量阶段
        Cooldown,       // 冷却阶段
        Complete        // 完成
    }
    
    public UnityEvent<float> OnMVCCalibrated;
    
    void Start()
    {
        StartCalibration();
    }
    
    void Update()
    {
        if (currentPhase != CalibrationPhase.Complete)
        {
            calibrationTimer += Time.deltaTime;
            UpdateCalibrationPhase();
            UpdateUI();
        }
    }
    
    public void StartCalibration()
    {
        currentPhase = CalibrationPhase.Instruction;
        calibrationTimer = 0f;
        sessionMaxForce = 0f;
        
        ShowInstructions();
    }
    
    private void ShowInstructions()
    {
        instructionText.text = "MVC握力测量\n\n" +
            "接下来将测量你的最大握力\n" +
            "请在听到提示音后用力握紧\n" +
            "并保持10秒钟\n\n" +
            "准备好了吗？";
        
        // 3秒后开始热身
        Invoke(nameof(StartWarmup), 3f);
    }
    
    private void StartWarmup()
    {
        currentPhase = CalibrationPhase.Warmup;
        calibrationTimer = 0f;
        
        instructionText.text = "准备开始...";
        StartCoroutine(WarmupCountdown());
    }
    
    private IEnumerator WarmupCountdown()
    {
        for (int i = 3; i > 0; i--)
        {
            countdownText.text = i.ToString();
            audioSource.PlayOneShot(countdownBeep);
            yield return new WaitForSeconds(1f);
        }
        
        countdownText.text = "用力握紧!";
        audioSource.PlayOneShot(startSound);
        
        StartMeasurement();
    }
    
    private void StartMeasurement()
    {
        currentPhase = CalibrationPhase.Measurement;
        calibrationTimer = 0f;
        
        instructionText.text = "用力握紧并保持!";
        instructionText.color = Color.red;
    }
    
    private void UpdateCalibrationPhase()
    {
        switch (currentPhase)
        {
            case CalibrationPhase.Measurement:
                if (calibrationTimer >= measurementDuration)
                {
                    StartCooldown();
                }
                break;
                
            case CalibrationPhase.Cooldown:
                if (calibrationTimer >= cooldownDuration)
                {
                    CompleteCalibration();
                }
                break;
        }
    }
    
    private void StartCooldown()
    {
        currentPhase = CalibrationPhase.Cooldown;
        calibrationTimer = 0f;
        
        instructionText.text = "很好! 现在请放松...";
        instructionText.color = Color.blue;
        countdownText.text = "";
    }
    
    private void CompleteCalibration()
    {
        currentPhase = CalibrationPhase.Complete;
        currentMVC = sessionMaxForce;
        
        instructionText.text = $"MVC测量完成!\n最大握力: {currentMVC:F1}";
        instructionText.color = Color.green;
        
        // 触发完成事件
        OnMVCCalibrated?.Invoke(currentMVC);
        
        // 保存MVC值
        DataManager.Instance.SetMVCValue(currentMVC);
        
        // 2秒后转到下一阶段
        Invoke(nameof(ProceedToNextPhase), 2f);
    }
    
    private void UpdateUI()
    {
        if (currentPhase == CalibrationPhase.Measurement)
        {
            // 更新倒计时
            float timeRemaining = measurementDuration - calibrationTimer;
            countdownText.text = $"{timeRemaining:F1}s";
            
            // 更新进度条
            forceProgressBar.value = calibrationTimer / measurementDuration;
        }
        
        // 更新力量显示
        currentForceText.text = $"当前: {BLEDeviceManager.Instance.currentForce:F1}";
        maxForceText.text = $"最大: {sessionMaxForce:F1}";
    }
    
    // 由BLE设备管理器调用
    public void OnForceDataReceived(float forceValue)
    {
        if (currentPhase == CalibrationPhase.Measurement)
        {
            if (forceValue > sessionMaxForce)
            {
                sessionMaxForce = forceValue;
                
                // 新记录特效
                if (encouragementEffect != null)
                    encouragementEffect.Play();
                    
                if (newRecordSound != null)
                    audioSource.PlayOneShot(newRecordSound);
            }
        }
    }
    
    private void ProceedToNextPhase()
    {
        // 转到REST校准
        GameStateManager.Instance.TransitionToState(GameState.RESTCalibration);
    }
}
```

### 6.2 游戏化REST测量
```csharp
public class RESTCalibrationGame : MonoBehaviour
{
    [Header("校准参数")]
    public float calibrationDuration = 10f;    // 校准持续时间
    public float stabilityThreshold = 2f;      // 稳定性阈值
    public float targetStabilityTime = 5f;     // 需要保持稳定的时间
    
    [Header("UI元素")]
    public Text instructionText;               // 指导文字
    public Slider stabilityBar;                // 稳定性进度条
    public Text forceValueText;                // 当前力量值
    public Image stabilityIndicator;           // 稳定性指示器
    public ParticleSystem relaxationEffect;    // 放松特效
    
    [Header("视觉反馈")]
    public Color stableColor = Color.green;
    public Color unstableColor = Color.red;
    
    private float currentRESTValue = 0f;
    private float stabilityTimer = 0f;
    private Queue<float> recentForceValues = new Queue<float>();
    private const int STABILITY_SAMPLE_SIZE = 50;
    
    private bool isCalibrationComplete = false;
    
    public UnityEvent<float> OnRESTCalibrated;
    
    void Start()
    {
        StartRESTCalibration();
    }
    
    void Update()
    {
        if (!isCalibrationComplete)
        {
            UpdateStabilityCheck();
            UpdateUI();
        }
    }
    
    public void StartRESTCalibration()
    {
        instructionText.text = "静息状态校准\n\n" +
            "请完全放松你的手\n" +
            "让握力回到自然状态\n" +
            "保持稳定5秒钟";
        
        isCalibrationComplete = false;
        stabilityTimer = 0f;
        recentForceValues.Clear();
    }
    
    private void UpdateStabilityCheck()
    {
        float currentForce = BLEDeviceManager.Instance.currentForce;
        
        // 记录最近的力量值
        recentForceValues.Enqueue(currentForce);
        if (recentForceValues.Count > STABILITY_SAMPLE_SIZE)
        {
            recentForceValues.Dequeue();
        }
        
        // 检查稳定性
        if (recentForceValues.Count >= STABILITY_SAMPLE_SIZE)
        {
            bool isStable = CheckStability();
            
            if (isStable)
            {
                stabilityTimer += Time.deltaTime;
                
                // 触发放松特效
                if (!relaxationEffect.isPlaying)
                    relaxationEffect.Play();
            }
            else
            {
                stabilityTimer = 0f;
                relaxationEffect.Stop();
            }
            
            // 检查是否完成校准
            if (stabilityTimer >= targetStabilityTime)
            {
                CompleteRESTCalibration();
            }
        }
    }
    
    private bool CheckStability()
    {
        if (recentForceValues.Count < STABILITY_SAMPLE_SIZE)
            return false;
        
        float[] values = recentForceValues.ToArray();
        float mean = values.Average();
        float variance = values.Sum(v => Mathf.Pow(v - mean, 2)) / values.Length;
        float stdDev = Mathf.Sqrt(variance);
        
        return stdDev <= stabilityThreshold;
    }
    
    private void UpdateUI()
    {
        float currentForce = BLEDeviceManager.Instance.currentForce;
        forceValueText.text = $"当前值: {currentForce:F2}";
        
        // 更新稳定性进度条
        stabilityBar.value = stabilityTimer / targetStabilityTime;
        
        // 更新稳定性指示器颜色
        bool isStable = CheckStability();
        stabilityIndicator.color = Color.Lerp(
            unstableColor, stableColor,
            isStable ? 1f : 0f
        );
        
        // 更新指导文字
        if (stabilityTimer > 0)
        {
            instructionText.text = $"很好! 保持稳定... ({stabilityTimer:F1}/{targetStabilityTime:F1}s)";
        }
        else
        {
            instructionText.text = "请放松手部，保持自然状态";
        }
    }
    
    private void CompleteRESTCalibration()
    {
        isCalibrationComplete = true;
        
        // 计算REST基线值
        currentRESTValue = recentForceValues.Average();
        
        instructionText.text = $"REST校准完成!\n基线值: {currentRESTValue:F2}";
        instructionText.color = Color.green;
        
        // 触发完成事件
        OnRESTCalibrated?.Invoke(currentRESTValue);
        
        // 保存REST值
        DataManager.Instance.SetRESTValue(currentRESTValue);
        
        // 2秒后开始游戏
        Invoke(nameof(StartGame), 2f);
    }
    
    private void StartGame()
    {
        GameStateManager.Instance.TransitionToState(GameState.GameLevels);
    }
}
```

## 7. 数据管理系统

### 7.1 数据存储结构
```csharp
public class DataManager : MonoBehaviour
{
    public static DataManager Instance;
    
    [System.Serializable]
    public class ExperimentSession
    {
        public string participantId;
        public string sessionId;
        public DateTime startTime;
        public DateTime endTime;
        public float mvcValue;
        public float restValue;
        public List<LevelResult> levelResults;
        public DeviceInfo deviceInfo;
        public string appVersion;
    }
    
    [System.Serializable]
    public class LevelResult
    {
        public int levelNumber;
        public string levelName;
        public float duration;
        public PerformanceMetrics performance;
        public List<ForceDataPoint> rawData;
        public GameLevel levelConfig;
        public DateTime completionTime;
    }
    
    [System.Serializable]
    public class DeviceInfo
    {
        public string deviceMAC;
        public string deviceModel;
        public string osVersion;
        public string appVersion;
        public float calibrationDate;
    }
    
    [Header("当前会话")]
    public ExperimentSession currentSession;
    public string currentParticipantId;
    
    [Header("存储配置")]
    public string dataFolderName = "ForceGameData";
    public bool autoSave = true;
    public float autoSaveInterval = 30f; // 自动保存间隔(秒)
    
    private string saveDirectory;
    
    void Awake()
    {
        Instance = this;
        InitializeDataStorage();
        
        if (autoSave)
        {
            InvokeRepeating(nameof(AutoSave), autoSaveInterval, autoSaveInterval);
        }
    }
    
    private void InitializeDataStorage()
    {
        saveDirectory = Path.Combine(Application.persistentDataPath, dataFolderName);
        
        if (!Directory.Exists(saveDirectory))
        {
            Directory.CreateDirectory(saveDirectory);
        }
    }
    
    public void StartNewSession(string participantId)
    {
        currentParticipantId = participantId;
        
        currentSession = new ExperimentSession
        {
            participantId = participantId,
            sessionId = GenerateSessionId(),
            startTime = DateTime.Now,
            levelResults = new List<LevelResult>(),
            deviceInfo = GetCurrentDeviceInfo(),
            appVersion = Application.version
        };
    }
    
    public void SetMVCValue(float mvc)
    {
        if (currentSession != null)
            currentSession.mvcValue = mvc;
    }
    
    public void SetRESTValue(float rest)
    {
        if (currentSession != null)
            currentSession.restValue = rest;
    }
    
    public void SaveLevelResult(int levelNumber, string levelName, 
        PerformanceMetrics performance, List<ForceDataPoint> rawData, GameLevel levelConfig)
    {
        if (currentSession == null) return;
        
        var levelResult = new LevelResult
        {
            levelNumber = levelNumber,
            levelName = levelName,
            duration = performance.duration,
            performance = performance,
            rawData = new List<ForceDataPoint>(rawData), // 深拷贝
            levelConfig = levelConfig,
            completionTime = DateTime.Now
        };
        
        currentSession.levelResults.Add(levelResult);
        
        if (autoSave)
        {
            AutoSave();
        }
    }
    
    public void SaveCurrentSession()
    {
        if (currentSession == null) return;
        
        currentSession.endTime = DateTime.Now;
        
        string fileName = $"{currentSession.participantId}_{currentSession.sessionId}.json";
        string filePath = Path.Combine(saveDirectory, fileName);
        
        try
        {
            string jsonData = JsonUtility.ToJson(currentSession, true);
            File.WriteAllText(filePath, jsonData);
            
            Debug.Log($"会话数据已保存到: {filePath}");
            
            // 同时保存CSV格式（便于分析）
            SaveAsCSV(currentSession, filePath.Replace(".json", ".csv"));
        }
        catch (Exception e)
        {
            Debug.LogError($"保存会话数据失败: {e.Message}");
        }
    }
    
    private void SaveAsCSV(ExperimentSession session, string csvPath)
    {
        try
        {
            using (var writer = new StreamWriter(csvPath))
            {
                // 写入头部信息
                writer.WriteLine($"# 参与者ID: {session.participantId}");
                writer.WriteLine($"# 会话ID: {session.sessionId}");
                writer.WriteLine($"# 开始时间: {session.startTime}");
                writer.WriteLine($"# 结束时间: {session.endTime}");
                writer.WriteLine($"# MVC值: {session.mvcValue}");
                writer.WriteLine($"# REST值: {session.restValue}");
                writer.WriteLine();
                
                // 写入CSV头
                writer.WriteLine("LevelNumber,LevelName,Timestamp,ActualForce,TargetForce,Deviation,InTolerance,EventMarker");
                
                // 写入每个关卡的数据
                foreach (var level in session.levelResults)
                {
                    foreach (var dataPoint in level.rawData)
                    {
                        writer.WriteLine($"{level.levelNumber},{level.levelName}," +
                            $"{dataPoint.timestamp:F3},{dataPoint.actualForce:F3}," +
                            $"{dataPoint.targetForce:F3},{dataPoint.deviation:F3}," +
                            $"{dataPoint.isInTolerance},{GetEventMarker(dataPoint, level)}");
                    }
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"保存CSV文件失败: {e.Message}");
        }
    }
    
    private string GetEventMarker(ForceDataPoint dataPoint, LevelResult level)
    {
        // 根据时间戳确定事件标记
        float normalizedTime = dataPoint.timestamp / level.duration;
        
        if (normalizedTime < 0.1f) return "START";
        if (normalizedTime > 0.9f) return "END";
        if (normalizedTime > 0.2f && normalizedTime < 0.8f) return "PLATEAU";
        return "TRANSITION";
    }
    
    private void AutoSave()
    {
        if (currentSession != null && currentSession.levelResults.Count > 0)
        {
            SaveCurrentSession();
        }
    }
    
    private string GenerateSessionId()
    {
        return DateTime.Now.ToString("yyyyMMdd_HHmmss");
    }
    
    private DeviceInfo GetCurrentDeviceInfo()
    {
        return new DeviceInfo
        {
            deviceMAC = BLEDeviceManager.Instance.targetDeviceMAC,
            deviceModel = SystemInfo.deviceModel,
            osVersion = SystemInfo.operatingSystem,
            appVersion = Application.version,
            calibrationDate = Time.time
        };
    }
    
    // 数据查询方法
    public List<string> GetAvailableParticipants()
    {
        var participants = new HashSet<string>();
        
        if (Directory.Exists(saveDirectory))
        {
            var files = Directory.GetFiles(saveDirectory, "*.json");
            
            foreach (var file in files)
            {
                try
                {
                    string jsonContent = File.ReadAllText(file);
                    var session = JsonUtility.FromJson<ExperimentSession>(jsonContent);
                    if (session != null)
                    {
                        participants.Add(session.participantId);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"读取文件失败 {file}: {e.Message}");
                }
            }
        }
        
        return participants.ToList();
    }
    
    public List<ExperimentSession> GetParticipantSessions(string participantId)
    {
        var sessions = new List<ExperimentSession>();
        
        if (Directory.Exists(saveDirectory))
        {
            var files = Directory.GetFiles(saveDirectory, $"{participantId}_*.json");
            
            foreach (var file in files)
            {
                try
                {
                    string jsonContent = File.ReadAllText(file);
                    var session = JsonUtility.FromJson<ExperimentSession>(jsonContent);
                    if (session != null)
                    {
                        sessions.Add(session);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"读取会话文件失败 {file}: {e.Message}");
                }
            }
        }
        
        return sessions.OrderByDescending(s => s.startTime).ToList();
    }
}
```

## 8. 音效和反馈系统

### 8.1 音频管理器
```csharp
public class AudioManager : MonoBehaviour
{
    public static AudioManager Instance;
    
    [Header("音效文件")]
    public AudioClip buttonClick;
    public AudioClip levelComplete;
    public AudioClip starEarned;
    public AudioClip perfectHit;
    public AudioClip goodHit;
    public AudioClip missHit;
    public AudioClip backgroundMusic;
    public AudioClip countdownBeep;
    public AudioClip gameStart;
    
    [Header("音频源")]
    public AudioSource musicSource;
    public AudioSource sfxSource;
    public AudioSource feedbackSource;
    
    [Header("音量设置")]
    [Range(0f, 1f)] public float masterVolume = 1f;
    [Range(0f, 1f)] public float musicVolume = 0.5f;
    [Range(0f, 1f)] public float sfxVolume = 0.8f;
    [Range(0f, 1f)] public float feedbackVolume = 0.6f;
    
    void Awake()
    {
        Instance = this;
        DontDestroyOnLoad(gameObject);
        
        // 初始化音频源
        if (musicSource == null)
            musicSource = gameObject.AddComponent<AudioSource>();
        if (sfxSource == null)
            sfxSource = gameObject.AddComponent<AudioSource>();
        if (feedbackSource == null)
            feedbackSource = gameObject.AddComponent<AudioSource>();
            
        // 配置音频源
        musicSource.loop = true;
        musicSource.volume = musicVolume * masterVolume;
        
        sfxSource.loop = false;
        sfxSource.volume = sfxVolume * masterVolume;
        
        feedbackSource.loop = false;
        feedbackSource.volume = feedbackVolume * masterVolume;
    }
    
    void Start()
    {
        PlayBackgroundMusic();
    }
    
    public void PlayBackgroundMusic()
    {
        if (backgroundMusic != null && !musicSource.isPlaying)
        {
            musicSource.clip = backgroundMusic;
            musicSource.Play();
        }
    }
    
    public void StopBackgroundMusic()
    {
        musicSource.Stop();
    }
    
    public void PlaySFX(AudioClip clip)
    {
        if (clip != null)
        {
            sfxSource.PlayOneShot(clip);
        }
    }
    
    public void PlayButtonClick()
    {
        PlaySFX(buttonClick);
    }
    
    public void PlayLevelComplete()
    {
        PlaySFX(levelComplete);
    }
    
    public void PlayStarSound()
    {
        PlaySFX(starEarned);
    }
    
    public void PlayPerformanceFeedback(float accuracy)
    {
        AudioClip feedbackClip;
        
        if (accuracy >= 90f)
            feedbackClip = perfectHit;
        else if (accuracy >= 70f)
            feedbackClip = goodHit;
        else
            feedbackClip = missHit;
            
        if (feedbackClip != null)
        {
            feedbackSource.PlayOneShot(feedbackClip);
        }
    }
    
    public void SetMasterVolume(float volume)
    {
        masterVolume = Mathf.Clamp01(volume);
        UpdateAllVolumes();
    }
    
    public void SetMusicVolume(float volume)
    {
        musicVolume = Mathf.Clamp01(volume);
        musicSource.volume = musicVolume * masterVolume;
    }
    
    public void SetSFXVolume(float volume)
    {
        sfxVolume = Mathf.Clamp01(volume);
        sfxSource.volume = sfxVolume * masterVolume;
    }
    
    private void UpdateAllVolumes()
    {
        musicSource.volume = musicVolume * masterVolume;
        sfxSource.volume = sfxVolume * masterVolume;
        feedbackSource.volume = feedbackVolume * masterVolume;
    }
}
```

### 8.2 触觉反馈系统
```csharp
public class HapticFeedbackManager : MonoBehaviour
{
    public static HapticFeedbackManager Instance;
    
    [Header("反馈强度")]
    [Range(0f, 1f)] public float lightVibration = 0.3f;
    [Range(0f, 1f)] public float mediumVibration = 0.6f;
    [Range(0f, 1f)] public float strongVibration = 1f;
    
    [Header("反馈开关")]
    public bool enableHaptics = true;
    
    void Awake()
    {
        Instance = this;
    }
    
    public void TriggerLightVibration()
    {
        if (!enableHaptics) return;
        
#if UNITY_ANDROID && !UNITY_EDITOR
        Handheld.Vibrate();
#elif UNITY_STANDALONE_WIN
        // Windows平台可以使用游戏手柄震动（如果连接了的话）
#endif
    }
    
    public void TriggerMediumVibration()
    {
        if (!enableHaptics) return;
        
#if UNITY_ANDROID && !UNITY_EDITOR
        // Android长震动
        AndroidVibrate(200);
#endif
    }
    
    public void TriggerStrongVibration()
    {
        if (!enableHaptics) return;
        
#if UNITY_ANDROID && !UNITY_EDITOR
        AndroidVibrate(500);
#endif
    }
    
    public void TriggerPerformanceFeedback(float accuracy)
    {
        if (accuracy >= 90f)
            TriggerLightVibration();  // 完美 - 轻震动
        else if (accuracy < 50f)
            TriggerMediumVibration(); // 差 - 中等震动
    }
    
#if UNITY_ANDROID && !UNITY_EDITOR
    private void AndroidVibrate(long milliseconds)
    {
        AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
        AndroidJavaObject vibrator = currentActivity.Call<AndroidJavaObject>("getSystemService", "vibrator");
        vibrator.Call("vibrate", milliseconds);
    }
#endif
    
    public void SetHapticsEnabled(bool enabled)
    {
        enableHaptics = enabled;
    }
}
```

## 9. 设置和配置系统

### 9.1 设置管理器
```csharp
public class SettingsManager : MonoBehaviour
{
    public static SettingsManager Instance;
    
    [System.Serializable]
    public class GameSettings
    {
        [Header("音频设置")]
        public float masterVolume = 1f;
        public float musicVolume = 0.5f;
        public float sfxVolume = 0.8f;
        public bool enableHaptics = true;
        
        [Header("游戏设置")]
        public float difficultyMultiplier = 1f;
        public bool showPerformanceDetails = true;
        public bool autoSaveEnabled = true;
        public int targetFrameRate = 60;
        
        [Header("设备设置")]
        public string preferredDeviceMAC = "";
        public float forceCalibrationOffset = 0f;
        public float forceCalibrationScale = 1f;
        
        [Header("界面设置")]
        public bool fullScreenMode = true;
        public int screenResolutionIndex = 0;
        public float uiScale = 1f;
    }
    
    [Header("当前设置")]
    public GameSettings currentSettings;
    
    private const string SETTINGS_KEY = "ForceGameSettings";
    
    void Awake()
    {
        Instance = this;
        DontDestroyOnLoad(gameObject);
        
        LoadSettings();
        ApplySettings();
    }
    
    public void LoadSettings()
    {
        if (PlayerPrefs.HasKey(SETTINGS_KEY))
        {
            string settingsJson = PlayerPrefs.GetString(SETTINGS_KEY);
            try
            {
                currentSettings = JsonUtility.FromJson<GameSettings>(settingsJson);
            }
            catch (Exception e)
            {
                Debug.LogWarning($"加载设置失败，使用默认设置: {e.Message}");
                currentSettings = new GameSettings();
            }
        }
        else
        {
            currentSettings = new GameSettings();
        }
    }
    
    public void SaveSettings()
    {
        try
        {
            string settingsJson = JsonUtility.ToJson(currentSettings, true);
            PlayerPrefs.SetString(SETTINGS_KEY, settingsJson);
            PlayerPrefs.Save();
            
            ApplySettings();
        }
        catch (Exception e)
        {
            Debug.LogError($"保存设置失败: {e.Message}");
        }
    }
    
    public void ApplySettings()
    {
        // 应用音频设置
        if (AudioManager.Instance != null)
        {
            AudioManager.Instance.SetMasterVolume(currentSettings.masterVolume);
            AudioManager.Instance.SetMusicVolume(currentSettings.musicVolume);
            AudioManager.Instance.SetSFXVolume(currentSettings.sfxVolume);
        }
        
        // 应用触觉反馈设置
        if (HapticFeedbackManager.Instance != null)
        {
            HapticFeedbackManager.Instance.SetHapticsEnabled(currentSettings.enableHaptics);
        }
        
        // 应用显示设置
        Application.targetFrameRate = currentSettings.targetFrameRate;
        
        // 应用全屏设置
        if (currentSettings.fullScreenMode != Screen.fullScreen)
        {
            Screen.fullScreen = currentSettings.fullScreenMode;
        }
        
        // 应用设备设置
        if (BLEDeviceManager.Instance != null && !string.IsNullOrEmpty(currentSettings.preferredDeviceMAC))
        {
            BLEDeviceManager.Instance.targetDeviceMAC = currentSettings.preferredDeviceMAC;
        }
    }
    
    public void ResetToDefaults()
    {
        currentSettings = new GameSettings();
        SaveSettings();
    }
    
    // 设置访问方法
    public void SetMasterVolume(float volume)
    {
        currentSettings.masterVolume = Mathf.Clamp01(volume);
        SaveSettings();
    }
    
    public void SetMusicVolume(float volume)
    {
        currentSettings.musicVolume = Mathf.Clamp01(volume);
        SaveSettings();
    }
    
    public void SetSFXVolume(float volume)
    {
        currentSettings.sfxVolume = Mathf.Clamp01(volume);
        SaveSettings();
    }
    
    public void SetHapticsEnabled(bool enabled)
    {
        currentSettings.enableHaptics = enabled;
        SaveSettings();
    }
    
    public void SetDifficultyMultiplier(float multiplier)
    {
        currentSettings.difficultyMultiplier = Mathf.Clamp(multiplier, 0.5f, 2f);
        SaveSettings();
    }
    
    public void SetPreferredDeviceMAC(string mac)
    {
        currentSettings.preferredDeviceMAC = mac;
        SaveSettings();
    }
}
```

### 9.2 设置界面
```csharp
public class SettingsUI : MonoBehaviour
{
    [Header("音频控制")]
    public Slider masterVolumeSlider;
    public Slider musicVolumeSlider;
    public Slider sfxVolumeSlider;
    public Toggle hapticsToggle;
    
    [Header("游戏设置")]
    public Slider difficultySlider;
    public Toggle performanceDetailsToggle;
    public Toggle autoSaveToggle;
    public Dropdown frameRateDropdown;
    
    [Header("设备设置")]
    public InputField deviceMACInput;
    public Button scanDevicesButton;
    public Dropdown availableDevicesDropdown;
    
    [Header("界面设置")]
    public Toggle fullScreenToggle;
    public Slider uiScaleSlider;
    
    [Header("操作按钮")]
    public Button saveButton;
    public Button resetButton;
    public Button backButton;
    
    void Start()
    {
        InitializeUI();
        LoadCurrentSettings();
        SetupEventListeners();
    }
    
    private void InitializeUI()
    {
        // 初始化帧率下拉菜单
        frameRateDropdown.options.Clear();
        frameRateDropdown.options.Add(new Dropdown.OptionData("30 FPS"));
        frameRateDropdown.options.Add(new Dropdown.OptionData("60 FPS"));
        frameRateDropdown.options.Add(new Dropdown.OptionData("120 FPS"));
        frameRateDropdown.options.Add(new Dropdown.OptionData("无限制"));
    }
    
    private void LoadCurrentSettings()
    {
        var settings = SettingsManager.Instance.currentSettings;
        
        // 加载音频设置
        masterVolumeSlider.value = settings.masterVolume;
        musicVolumeSlider.value = settings.musicVolume;
        sfxVolumeSlider.value = settings.sfxVolume;
        hapticsToggle.isOn = settings.enableHaptics;
        
        // 加载游戏设置
        difficultySlider.value = settings.difficultyMultiplier;
        performanceDetailsToggle.isOn = settings.showPerformanceDetails;
        autoSaveToggle.isOn = settings.autoSaveEnabled;
        
        // 设置帧率
        switch (settings.targetFrameRate)
        {
            case 30: frameRateDropdown.value = 0; break;
            case 60: frameRateDropdown.value = 1; break;
            case 120: frameRateDropdown.value = 2; break;
            default: frameRateDropdown.value = 3; break;
        }
        
        // 加载设备设置
        deviceMACInput.text = settings.preferredDeviceMAC;
        
        // 加载界面设置
        fullScreenToggle.isOn = settings.fullScreenMode;
        uiScaleSlider.value = settings.uiScale;
    }
    
    private void SetupEventListeners()
    {
        // 音频设置监听
        masterVolumeSlider.onValueChanged.AddListener(OnMasterVolumeChanged);
        musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);
        sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);
        hapticsToggle.onValueChanged.AddListener(OnHapticsToggled);
        
        // 游戏设置监听
        difficultySlider.onValueChanged.AddListener(OnDifficultyChanged);
        performanceDetailsToggle.onValueChanged.AddListener(OnPerformanceDetailsToggled);
        autoSaveToggle.onValueChanged.AddListener(OnAutoSaveToggled);
        frameRateDropdown.onValueChanged.AddListener(OnFrameRateChanged);
        
        // 设备设置监听
        deviceMACInput.onEndEdit.AddListener(OnDeviceMACChanged);
        scanDevicesButton.onClick.AddListener(OnScanDevicesClicked);
        availableDevicesDropdown.onValueChanged.AddListener(OnDeviceSelected);
        
        // 界面设置监听
        fullScreenToggle.onValueChanged.AddListener(OnFullScreenToggled);
        uiScaleSlider.onValueChanged.AddListener(OnUIScaleChanged);
        
        // 操作按钮监听
        saveButton.onClick.AddListener(OnSaveSettings);
        resetButton.onClick.AddListener(OnResetSettings);
        backButton.onClick.AddListener(OnBackClicked);
    }
    
    // 事件处理方法
    private void OnMasterVolumeChanged(float value)
    {
        SettingsManager.Instance.SetMasterVolume(value);
    }
    
    private void OnMusicVolumeChanged(float value)
    {
        SettingsManager.Instance.SetMusicVolume(value);
    }
    
    private void OnSFXVolumeChanged(float value)
    {
        SettingsManager.Instance.SetSFXVolume(value);
    }
    
    private void OnHapticsToggled(bool value)
    {
        SettingsManager.Instance.SetHapticsEnabled(value);
    }
    
    private void OnDifficultyChanged(float value)
    {
        SettingsManager.Instance.SetDifficultyMultiplier(value);
    }
    
    private void OnPerformanceDetailsToggled(bool value)
    {
        SettingsManager.Instance.currentSettings.showPerformanceDetails = value;
    }
    
    private void OnAutoSaveToggled(bool value)
    {
        SettingsManager.Instance.currentSettings.autoSaveEnabled = value;
    }
    
    private void OnFrameRateChanged(int index)
    {
        int[] frameRates = { 30, 60, 120, -1 };
        SettingsManager.Instance.currentSettings.targetFrameRate = frameRates[index];
    }
    
    private void OnDeviceMACChanged(string mac)
    {
        SettingsManager.Instance.SetPreferredDeviceMAC(mac);
    }
    
    private async void OnScanDevicesClicked()
    {
        scanDevicesButton.interactable = false;
        scanDevicesButton.GetComponentInChildren<Text>().text = "扫描中...";
        
        try
        {
            var devices = await BLEDeviceManager.Instance.ScanForDevices();
            UpdateAvailableDevices(devices);
        }
        catch (Exception e)
        {
            Debug.LogError($"设备扫描失败: {e.Message}");
        }
        finally
        {
            scanDevicesButton.interactable = true;
            scanDevicesButton.GetComponentInChildren<Text>().text = "扫描设备";
        }
    }
    
    private void UpdateAvailableDevices(List<string> devices)
    {
        availableDevicesDropdown.options.Clear();
        foreach (var device in devices)
        {
            availableDevicesDropdown.options.Add(new Dropdown.OptionData(device));
        }
        availableDevicesDropdown.RefreshShownValue();
    }
    
    private void OnDeviceSelected(int index)
    {
        if (index >= 0 && index < availableDevicesDropdown.options.Count)
        {
            string selectedDevice = availableDevicesDropdown.options[index].text;
            deviceMACInput.text = selectedDevice;
            OnDeviceMACChanged(selectedDevice);
        }
    }
    
    private void OnFullScreenToggled(bool value)
    {
        SettingsManager.Instance.currentSettings.fullScreenMode = value;
    }
    
    private void OnUIScaleChanged(float value)
    {
        SettingsManager.Instance.currentSettings.uiScale = value;
        // 实时应用UI缩放
        ApplyUIScale(value);
    }
    
    private void ApplyUIScale(float scale)
    {
        var canvasScaler = FindObjectOfType<UnityEngine.UI.CanvasScaler>();
        if (canvasScaler != null)
        {
            canvasScaler.scaleFactor = scale;
        }
    }
    
    private void OnSaveSettings()
    {
        SettingsManager.Instance.SaveSettings();
        AudioManager.Instance.PlayButtonClick();
        
        // 显示保存成功提示
        StartCoroutine(ShowSaveConfirmation());
    }
    
    private IEnumerator ShowSaveConfirmation()
    {
        var originalText = saveButton.GetComponentInChildren<Text>().text;
        saveButton.GetComponentInChildren<Text>().text = "已保存!";
        saveButton.interactable = false;
        
        yield return new WaitForSeconds(1f);
        
        saveButton.GetComponentInChildren<Text>().text = originalText;
        saveButton.interactable = true;
    }
    
    private void OnResetSettings()
    {
        // 显示确认对话框
        ConfirmationDialog.Instance.Show(
            "重置设置",
            "确定要重置所有设置到默认值吗？",
            () => {
                SettingsManager.Instance.ResetToDefaults();
                LoadCurrentSettings();
                AudioManager.Instance.PlayButtonClick();
            },
            () => {
                AudioManager.Instance.PlayButtonClick();
            }
        );
    }
    
    private void OnBackClicked()
    {
        AudioManager.Instance.PlayButtonClick();
        GameStateManager.Instance.TransitionToState(GameState.MainMenu);
    }
}
```

## 10. 主菜单和导航系统

### 10.1 主菜单管理器
```csharp
public class MainMenuManager : MonoBehaviour
{
    [Header("主菜单UI")]
    public GameObject mainMenuPanel;
    public InputField participantIdInput;
    public Button startGameButton;
    public Button settingsButton;
    public Button historyButton;
    public Button quitButton;
    
    [Header("设备状态")]
    public GameObject deviceStatusPanel;
    public Text deviceStatusText;
    public Image deviceStatusIcon;
    public Button reconnectButton;
    
    [Header("参与者历史")]
    public Dropdown participantHistoryDropdown;
    public Button loadParticipantButton;
    
    [Header("版本信息")]
    public Text versionText;
    public Text buildDateText;
    
    void Start()
    {
        InitializeMainMenu();
        SetupEventListeners();
        CheckDeviceConnection();
        LoadParticipantHistory();
    }
    
    void Update()
    {
        UpdateDeviceStatus();
    }
    
    private void InitializeMainMenu()
    {
        // 显示版本信息
        versionText.text = $"版本 {Application.version}";
        buildDateText.text = $"构建日期: {GetBuildDate()}";
        
        // 设置默认参与者ID
        if (string.IsNullOrEmpty(participantIdInput.text))
        {
            participantIdInput.text = GenerateDefaultParticipantId();
        }
        
        // 检查开始游戏按钮状态
        UpdateStartButtonState();
    }
    
    private void SetupEventListeners()
    {
        participantIdInput.onValueChanged.AddListener(OnParticipantIdChanged);
        startGameButton.onClick.AddListener(OnStartGameClicked);
        settingsButton.onClick.AddListener(OnSettingsClicked);
        historyButton.onClick.AddListener(OnHistoryClicked);
        quitButton.onClick.AddListener(OnQuitClicked);
        
        reconnectButton.onClick.AddListener(OnReconnectClicked);
        loadParticipantButton.onClick.AddListener(OnLoadParticipantClicked);
    }
    
    private void CheckDeviceConnection()
    {
        StartCoroutine(DeviceConnectionCheck());
    }
    
    private IEnumerator DeviceConnectionCheck()
    {
        yield return new WaitForSeconds(1f); // 等待初始化完成
        
        if (BLEDeviceManager.Instance != null)
        {
            bool connected = await BLEDeviceManager.Instance.CheckConnection();
            UpdateDeviceStatusUI(connected);
            
            if (!connected)
            {
                // 尝试自动连接
                yield return StartCoroutine(AutoConnectDevice());
            }
        }
    }
    
    private IEnumerator AutoConnectDevice()
    {
        deviceStatusText.text = "正在连接设备...";
        
        bool connected = await BLEDeviceManager.Instance.ConnectToDevice();
        UpdateDeviceStatusUI(connected);
        
        yield return null;
    }
    
    private void UpdateDeviceStatus()
    {
        if (BLEDeviceManager.Instance != null)
        {
            bool isConnected = BLEDeviceManager.Instance.isConnected;
            UpdateDeviceStatusUI(isConnected);
        }
    }
    
    private void UpdateDeviceStatusUI(bool connected)
    {
        if (connected)
        {
            deviceStatusText.text = "设备已连接";
            deviceStatusText.color = Color.green;
            deviceStatusIcon.color = Color.green;
            reconnectButton.gameObject.SetActive(false);
        }
        else
        {
            deviceStatusText.text = "设备未连接";
            deviceStatusText.color = Color.red;
            deviceStatusIcon.color = Color.red;
            reconnectButton.gameObject.SetActive(true);
        }
        
        UpdateStartButtonState();
    }
    
    private void LoadParticipantHistory()
    {
        var participants = DataManager.Instance.GetAvailableParticipants();
        
        participantHistoryDropdown.options.Clear();
        participantHistoryDropdown.options.Add(new Dropdown.OptionData("选择历史参与者..."));
        
        foreach (var participant in participants)
        {
            participantHistoryDropdown.options.Add(new Dropdown.OptionData(participant));
        }
        
        participantHistoryDropdown.RefreshShownValue();
        participantHistoryDropdown.value = 0;
    }
    
    private void UpdateStartButtonState()
    {
        bool canStart = !string.IsNullOrEmpty(participantIdInput.text.Trim()) &&
                       BLEDeviceManager.Instance != null &&
                       BLEDeviceManager.Instance.isConnected;
        
        startGameButton.interactable = canStart;
        startGameButton.GetComponent<Image>().color = canStart ? Color.green : Color.gray;
    }
    
    // 事件处理方法
    private void OnParticipantIdChanged(string value)
    {
        UpdateStartButtonState();
    }
    
    private void OnStartGameClicked()
    {
        string participantId = participantIdInput.text.Trim();
        
        if (string.IsNullOrEmpty(participantId))
        {
            ShowMessage("错误", "请输入参与者ID");
            return;
        }
        
        if (!BLEDeviceManager.Instance.isConnected)
        {
            ShowMessage("错误", "设备未连接，请检查设备状态");
            return;
        }
        
        // 开始新的实验会话
        DataManager.Instance.StartNewSession(participantId);
        
        // 播放按钮音效
        AudioManager.Instance.PlayButtonClick();
        
        // 转换到教程状态
        GameStateManager.Instance.TransitionToState(GameState.Tutorial);
    }
    
    private void OnSettingsClicked()
    {
        AudioManager.Instance.PlayButtonClick();
        GameStateManager.Instance.TransitionToState(GameState.Settings);
    }
    
    private void OnHistoryClicked()
    {
        AudioManager.Instance.PlayButtonClick();
        // 显示历史记录界面
        HistoryViewerUI.Instance.Show();
    }
    
    private void OnQuitClicked()
    {
        AudioManager.Instance.PlayButtonClick();
        
        ConfirmationDialog.Instance.Show(
            "退出游戏",
            "确定要退出游戏吗？",
            () => {
                Application.Quit();
#if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
#endif
            },
            null
        );
    }
    
    private async void OnReconnectClicked()
    {
        AudioManager.Instance.PlayButtonClick();
        
        reconnectButton.interactable = false;
        var buttonText = reconnectButton.GetComponentInChildren<Text>();
        var originalText = buttonText.text;
        buttonText.text = "连接中...";
        
        try
        {
            bool connected = await BLEDeviceManager.Instance.ConnectToDevice();
            UpdateDeviceStatusUI(connected);
            
            if (connected)
            {
                ShowMessage("成功", "设备连接成功!");
            }
            else
            {
                ShowMessage("失败", "设备连接失败，请检查设备是否开启");
            }
        }
        catch (Exception e)
        {
            ShowMessage("错误", $"连接过程中发生错误: {e.Message}");
        }
        finally
        {
            reconnectButton.interactable = true;
            buttonText.text = originalText;
        }
    }
    
    private void OnLoadParticipantClicked()
    {
        if (participantHistoryDropdown.value > 0)
        {
            string selectedParticipant = participantHistoryDropdown.options[participantHistoryDropdown.value].text;
            participantIdInput.text = selectedParticipant;
            AudioManager.Instance.PlayButtonClick();
        }
    }
    
    // 辅助方法
    private string GenerateDefaultParticipantId()
    {
        return $"P{DateTime.Now:yyyyMMdd_HHmm}";
    }
    
    private string GetBuildPath(BuildTarget target)
    {
        string basePath = Path.Combine(Application.dataPath, "..", "Builds");
        string platformName = target.ToString();
        string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
        
        switch (target)
        {
            case BuildTarget.Android:
                return Path.Combine(basePath, "Android", $"ForceGame_{currentConfig.buildVersion}_{timestamp}.apk");
            case BuildTarget.StandaloneWindows64:
                return Path.Combine(basePath, "Windows", $"ForceGame_{currentConfig.buildVersion}_{timestamp}");
            default:
                return Path.Combine(basePath, platformName, $"ForceGame_{currentConfig.buildVersion}_{timestamp}");
        }
    }
    
    private BuildOptions GetBuildOptions()
    {
        BuildOptions options = BuildOptions.None;
        
        if (currentConfig.developmentBuild)
            options |= BuildOptions.Development;
            
        if (currentConfig.enableDebugging && currentConfig.developmentBuild)
            options |= BuildOptions.AllowDebugging;
            
        if (currentConfig.enableProfiling && currentConfig.developmentBuild)
            options |= BuildOptions.ConnectWithProfiler;
        
        return options;
    }
    
    private void OpenBuildFolder()
    {
        string buildPath = Path.Combine(Application.dataPath, "..", "Builds");
        
        if (Directory.Exists(buildPath))
        {
            EditorUtility.RevealInFinder(buildPath);
        }
        else
        {
            Directory.CreateDirectory(buildPath);
            EditorUtility.RevealInFinder(buildPath);
        }
    }
#endif
}
```

### 13.2 平台特定配置
```csharp
public class PlatformConfiguration : MonoBehaviour
{
    [Header("平台检测")]
    public RuntimePlatform currentPlatform;
    
    [Header("Android配置")]
    public AndroidConfiguration androidConfig;
    
    [Header("Windows配置")]
    public WindowsConfiguration windowsConfig;
    
    [System.Serializable]
    public class AndroidConfiguration
    {
        public bool useLocationServices = false;
        public bool preventScreenDimming = true;
        public ScreenOrientation forcedOrientation = ScreenOrientation.LandscapeLeft;
        public int targetFrameRate = 60;
        public bool enableHaptics = true;
        public string bluetoothPermissionReason = "需要蓝牙权限来连接握力计设备";
    }
    
    [System.Serializable]
    public class WindowsConfiguration
    {
        public bool allowFullscreen = true;
        public bool allowWindowResize = true;
        public Vector2Int defaultResolution = new Vector2Int(1920, 1080);
        public int targetFrameRate = 120;
        public bool enableVSync = true;
    }
    
    void Awake()
    {
        currentPlatform = Application.platform;
        ApplyPlatformConfiguration();
    }
    
    private void ApplyPlatformConfiguration()
    {
        switch (currentPlatform)
        {
            case RuntimePlatform.Android:
                ApplyAndroidConfiguration();
                break;
                
            case RuntimePlatform.WindowsPlayer:
            case RuntimePlatform.WindowsEditor:
                ApplyWindowsConfiguration();
                break;
        }
    }
    
    private void ApplyAndroidConfiguration()
    {
        Debug.Log("应用Android平台配置");
        
        // 屏幕方向
        Screen.orientation = androidConfig.forcedOrientation;
        Screen.autorotateToLandscapeLeft = false;
        Screen.autorotateToLandscapeRight = false;
        Screen.autorotateToPortrait = false;
        Screen.autorotateToPortraitUpsideDown = false;
        
        // 防止屏幕变暗
        if (androidConfig.preventScreenDimming)
        {
            Screen.sleepTimeout = SleepTimeout.NeverSleep;
        }
        
        // 帧率设置
        Application.targetFrameRate = androidConfig.targetFrameRate;
        
        // 请求权限
        RequestAndroidPermissions();
    }
    
    private void ApplyWindowsConfiguration()
    {
        Debug.Log("应用Windows平台配置");
        
        // 分辨率设置
        Screen.SetResolution(
            windowsConfig.defaultResolution.x,
            windowsConfig.defaultResolution.y,
            windowsConfig.allowFullscreen
        );
        
        // 帧率和垂直同步
        Application.targetFrameRate = windowsConfig.targetFrameRate;
        QualitySettings.vSyncCount = windowsConfig.enableVSync ? 1 : 0;
    }
    
    private void RequestAndroidPermissions()
    {
#if UNITY_ANDROID && !UNITY_EDITOR
        // 请求蓝牙权限
        if (!Permission.HasUserAuthorizedPermission("android.permission.BLUETOOTH"))
        {
            Permission.RequestUserPermission("android.permission.BLUETOOTH");
        }
        
        if (!Permission.HasUserAuthorizedPermission("android.permission.BLUETOOTH_ADMIN"))
        {
            Permission.RequestUserPermission("android.permission.BLUETOOTH_ADMIN");
        }
        
        // Android 12+ 需要的新权限
        if (!Permission.HasUserAuthorizedPermission("android.permission.BLUETOOTH_SCAN"))
        {
            Permission.RequestUserPermission("android.permission.BLUETOOTH_SCAN");
        }
        
        if (!Permission.HasUserAuthorizedPermission("android.permission.BLUETOOTH_CONNECT"))
        {
            Permission.RequestUserPermission("android.permission.BLUETOOTH_CONNECT");
        }
        
        // 位置权限（BLE扫描需要）
        if (!Permission.HasUserAuthorizedPermission(Permission.FineLocation))
        {
            Permission.RequestUserPermission(Permission.FineLocation);
        }
#endif
    }
    
    public bool HasRequiredPermissions()
    {
#if UNITY_ANDROID && !UNITY_EDITOR
        return Permission.HasUserAuthorizedPermission("android.permission.BLUETOOTH") &&
               Permission.HasUserAuthorizedPermission("android.permission.BLUETOOTH_ADMIN") &&
               Permission.HasUserAuthorizedPermission(Permission.FineLocation);
#else
        return true;
#endif
    }
    
    public void ShowPermissionDialog()
    {
        if (!HasRequiredPermissions())
        {
            MessageDialog.Instance.Show(
                "需要权限",
                "应用需要蓝牙和位置权限来连接握力计设备。请在设置中授予相关权限。",
                () => {
                    OpenAppSettings();
                }
            );
        }
    }
    
    private void OpenAppSettings()
    {
#if UNITY_ANDROID && !UNITY_EDITOR
        try
        {
            AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
            AndroidJavaObject intent = new AndroidJavaObject("android.content.Intent");
            
            intent.Call<AndroidJavaObject>("setAction", "android.settings.APPLICATION_DETAILS_SETTINGS");
            AndroidJavaClass uri = new AndroidJavaClass("android.net.Uri");
            AndroidJavaObject uriObject = uri.CallStatic<AndroidJavaObject>("parse", "package:" + Application.identifier);
            intent.Call<AndroidJavaObject>("setData", uriObject);
            
            currentActivity.Call("startActivity", intent);
        }
        catch (Exception e)
        {
            Debug.LogError($"打开应用设置失败: {e.Message}");
        }
#endif
    }
}
```

## 14. 测试和调试支持

### 14.1 开发者工具面板
```csharp
public class DeveloperTools : MonoBehaviour
{
    public static DeveloperTools Instance;
    
    [Header("开发者模式")]
    public bool developerModeEnabled = false;
    public KeyCode toggleKey = KeyCode.F12;
    
    [Header("调试UI")]
    public GameObject debugPanel;
    public Text debugInfoText;
    public Button simulateDeviceButton;
    public Button generateTestDataButton;
    public Button clearDataButton;
    public Button exportLogsButton;
    public Slider simulatedForceSlider;
    
    [Header("性能监控")]
    public Text fpsText;
    public Text memoryText;
    public Text deviceStatusText;
    
    private bool debugPanelVisible = false;
    private float[] fpsHistory = new float[60];
    private int fpsIndex = 0;
    
    void Awake()
    {
        Instance = this;
        
        // 只在开发构建中启用
        developerModeEnabled = Debug.isDebugBuild;
    }
    
    void Start()
    {
        if (debugPanel != null)
        {
            debugPanel.SetActive(false);
        }
        
        SetupDeveloperTools();
    }
    
    void Update()
    {
        if (developerModeEnabled)
        {
            HandleDebugInput();
            UpdatePerformanceInfo();
            
            if (debugPanelVisible)
            {
                UpdateDebugInfo();
            }
        }
    }
    
    private void HandleDebugInput()
    {
        if (Input.GetKeyDown(toggleKey))
        {
            ToggleDebugPanel();
        }
        
        // 快捷键支持
        if (Input.GetKeyDown(KeyCode.F1))
        {
            SimulateDeviceConnection();
        }
        
        if (Input.GetKeyDown(KeyCode.F2))
        {
            GenerateTestData();
        }
    }
    
    private void SetupDeveloperTools()
    {
        if (!developerModeEnabled) return;
        
        if (simulateDeviceButton != null)
            simulateDeviceButton.onClick.AddListener(SimulateDeviceConnection);
            
        if (generateTestDataButton != null)
            generateTestDataButton.onClick.AddListener(GenerateTestData);
            
        if (clearDataButton != null)
            clearDataButton.onClick.AddListener(ClearAllData);
            
        if (exportLogsButton != null)
            exportLogsButton.onClick.AddListener(ExportLogs);
            
        if (simulatedForceSlider != null)
            simulatedForceSlider.onValueChanged.AddListener(OnSimulatedForceChanged);
    }
    
    private void ToggleDebugPanel()
    {
        debugPanelVisible = !debugPanelVisible;
        
        if (debugPanel != null)
        {
            debugPanel.SetActive(debugPanelVisible);
        }
    }
    
    private void UpdatePerformanceInfo()
    {
        // 更新FPS
        fpsHistory[fpsIndex] = 1.0f / Time.unscaledDeltaTime;
        fpsIndex = (fpsIndex + 1) % fpsHistory.Length;
        
        if (fpsText != null)
        {
            float avgFPS = fpsHistory.Average();
            fpsText.text = $"FPS: {avgFPS:F1}";
        }
        
        // 更新内存使用
        if (memoryText != null)
        {
            long memoryUsage = System.GC.GetTotalMemory(false) / (1024 * 1024);
            memoryText.text = $"内存: {memoryUsage} MB";
        }
        
        // 更新设备状态
        if (deviceStatusText != null && BLEDeviceManager.Instance != null)
        {
            string status = BLEDeviceManager.Instance.isConnected ? "已连接" : "未连接";
            float currentForce = BLEDeviceManager.Instance.currentForce;
            deviceStatusText.text = $"设备: {status} | 力值: {currentForce:F2}";
        }
    }
    
    private void UpdateDebugInfo()
    {
        if (debugInfoText == null) return;
        
        var info = new System.Text.StringBuilder();
        
        // 游戏状态信息
        info.AppendLine($"游戏状态: {GameStateManager.Instance?.currentState}");
        info.AppendLine($"当前关卡: {LevelManager.Instance?.currentLevelIndex}");
        info.AppendLine($"参与者: {DataManager.Instance?.currentParticipantId}");
        
        // 设备信息
        if (BLEDeviceManager.Instance != null)
        {
            info.AppendLine($"设备连接: {BLEDeviceManager.Instance.isConnected}");
            info.AppendLine($"当前力值: {BLEDeviceManager.Instance.currentForce:F3}");
        }
        
        // 校准信息
        if (DataManager.Instance?.currentSession != null)
        {
            info.AppendLine($"MVC值: {DataManager.Instance.currentSession.mvcValue:F2}");
            info.AppendLine($"REST值: {DataManager.Instance.currentSession.restValue:F2}");
        }
        
        // 性能信息
        info.AppendLine($"平均FPS: {fpsHistory.Average():F1}");
        info.AppendLine($"时间缩放: {Time.timeScale:F2}");
        
        debugInfoText.text = info.ToString();
    }
    
    private void SimulateDeviceConnection()
    {
        if (BLEDeviceManager.Instance != null)
        {
            // 模拟设备连接
            BLEDeviceManager.Instance.isConnected = !BLEDeviceManager.Instance.isConnected;
            
            string status = BLEDeviceManager.Instance.isConnected ? "已连接" : "已断开";
            Debug.Log($"模拟设备{status}");
            
            if (BLEDeviceManager.Instance.isConnected)
            {
                BLEDeviceManager.Instance.OnDeviceConnected?.Invoke();
            }
            else
            {
                BLEDeviceManager.Instance.OnDeviceDisconnected?.Invoke();
            }
        }
    }
    
    private void GenerateTestData()
    {
        if (DataManager.Instance == null) return;
        
        Debug.Log("生成测试数据...");
        
        // 生成模拟的实验会话数据
        string testParticipantId = $"TEST_{DateTime.Now:HHmmss}";
        DataManager.Instance.StartNewSession(testParticipantId);
        DataManager.Instance.SetMVCValue(UnityEngine.Random.Range(80f, 120f));
        DataManager.Instance.SetRESTValue(UnityEngine.Random.Range(0f, 5f));
        
        // 生成几个关卡的测试数据
        for (int level = 1; level <= 3; level++)
        {
            var testData = GenerateTestLevelData(level);
            var testMetrics = GenerateTestPerformanceMetrics();
            var testLevelConfig = new GameLevel
            {
                levelNumber = level,
                levelName = $"测试关卡{level}",
                duration = 30f
            };
            
            DataManager.Instance.SaveLevelResult(
                level, 
                $"测试关卡{level}", 
                testMetrics, 
                testData, 
                testLevelConfig
            );
        }
        
        DataManager.Instance.SaveCurrentSession();
        Debug.Log($"测试数据生成完成: 参与者ID = {testParticipantId}");
    }
    
    private List<ForceDataPoint> GenerateTestLevelData(int levelNumber)
    {
        var data = new List<ForceDataPoint>();
        float duration = 30f;
        float sampleRate = 20f; // 模拟20Hz采样率
        int totalSamples = Mathf.RoundToInt(duration * sampleRate);
        
        for (int i = 0; i < totalSamples; i++)
        {
            float timestamp = i / sampleRate;
            float normalizedTime = timestamp / duration;
            
            // 生成模拟的目标和实际力值
            float targetForce = 10f + levelNumber * 5f + 
                Mathf.Sin(normalizedTime * Mathf.PI * 2f) * 5f;
            float actualForce = targetForce + 
                UnityEngine.Random.Range(-3f, 3f) + 
                Mathf.Sin(timestamp * 10f) * 2f;
            
            var dataPoint = new ForceDataPoint
            {
                timestamp = timestamp,
                actualForce = Mathf.Max(0, actualForce),
                targetForce = Mathf.Max(0, targetForce),
                deviation = Mathf.Abs(actualForce - targetForce),
                isInTolerance = Mathf.Abs(actualForce - targetForce) < 5f
            };
            
            data.Add(dataPoint);
        }
        
        return data;
    }
    
    private PerformanceMetrics GenerateTestPerformanceMetrics()
    {
        return new PerformanceMetrics
        {
            accuracy = UnityEngine.Random.Range(60f, 95f),
            consistency = UnityEngine.Random.Range(70f, 90f),
            responseTime = UnityEngine.Random.Range(200f, 800f),
            finalScore = UnityEngine.Random.Range(500f, 950f),
            stars = UnityEngine.Random.Range(1, 4),
            duration = 30f,
            detailedMetrics = new Dictionary<string, float>
            {
                ["TolerancePercentage"] = UnityEngine.Random.Range(60f, 85f),
                ["MaxDeviation"] = UnityEngine.Random.Range(8f, 20f),
                ["AvgDeviation"] = UnityEngine.Random.Range(3f, 8f),
                ["ForceRange"] = UnityEngine.Random.Range(15f, 40f)
            }
        };
    }
    
    private void ClearAllData()
    {
        ConfirmationDialog.Instance.Show(
            "清除所有数据",
            "确定要删除所有实验数据吗？此操作无法撤销！",
            () => {
                try
                {
                    string dataPath = Path.Combine(Application.persistentDataPath, "ForceGameData");
                    if (Directory.Exists(dataPath))
                    {
                        Directory.Delete(dataPath, true);
                        Directory.CreateDirectory(dataPath);
                    }
                    
                    PlayerPrefs.DeleteAll();
                    PlayerPrefs.Save();
                    
                    Debug.Log("所有数据已清除");
                    MessageDialog.Instance.Show("完成", "所有数据已清除");
                }
                catch (Exception e)
                {
                    Debug.LogError($"清除数据失败: {e.Message}");
                }
            },
            null
        );
    }
    
    private void ExportLogs()
    {
        try
        {
            string exportPath = Path.Combine(Application.persistentDataPath, "Exports");
            if (!Directory.Exists(exportPath))
            {
                Directory.CreateDirectory(exportPath);
            }
            
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string logFileName = $"debug_logs_{timestamp}.txt";
            string fullPath = Path.Combine(exportPath, logFileName);
            
            var logContent = new System.Text.StringBuilder();
            logContent.AppendLine($"调试日志导出时间: {DateTime.Now}");
            logContent.AppendLine($"应用版本: {Application.version}");
            logContent.AppendLine($"Unity版本: {Application.unityVersion}");
            logContent.AppendLine($"平台: {Application.platform}");
            logContent.AppendLine();
            
            // 添加错误日志
            if (ExceptionManager.Instance != null)
            {
                var errorLogs = ExceptionManager.Instance.GetRecentErrorLogs(50);
                if (errorLogs.Count > 0)
                {
                    logContent.AppendLine("=== 最近的错误日志 ===");
                    foreach (var log in errorLogs)
                    {
                        logContent.AppendLine(log);
                    }
                    logContent.AppendLine();
                }
            }
            
            // 添加系统信息
            logContent.AppendLine("=== 系统信息 ===");
            logContent.AppendLine($"设备型号: {SystemInfo.deviceModel}");
            logContent.AppendLine($"操作系统: {SystemInfo.operatingSystem}");
            logContent.AppendLine($"内存大小: {SystemInfo.systemMemorySize} MB");
            logContent.AppendLine($"图形设备: {SystemInfo.graphicsDeviceName}");
            
            File.WriteAllText(fullPath, logContent.ToString());
            
            Debug.Log($"日志已导出到: {fullPath}");
            MessageDialog.Instance.Show("导出完成", $"日志已导出到:\n{fullPath}");
        }
        catch (Exception e)
        {
            Debug.LogError($"导出日志失败: {e.Message}");
        }
    }
    
    private void OnSimulatedForceChanged(float value)
    {
        if (BLEDeviceManager.Instance != null && BLEDeviceManager.Instance.isConnected)
        {
            // 模拟力值输入
            float simulatedForce = value * 100f; // 转换为百分比
            BLEDeviceManager.Instance.OnForceDataReceived?.Invoke(simulatedForce);
        }
    }
    
    // 公共接口方法
    public void EnableDeveloperMode(bool enable)
    {
        developerModeEnabled = enable;
        
        if (!enable && debugPanel != null)
        {
            debugPanel.SetActive(false);
            debugPanelVisible = false;
        }
    }
    
    public void LogDebugMessage(string message)
    {
        if (developerModeEnabled)
        {
            Debug.Log($"[DEV] {message}");
        }
    }
    
    public Dictionary<string, object> GetSystemInfo()
    {
        return new Dictionary<string, object>
        {
            ["Platform"] = Application.platform.ToString(),
            ["UnityVersion"] = Application.unityVersion,
            ["AppVersion"] = Application.version,
            ["DeviceModel"] = SystemInfo.deviceModel,
            ["OS"] = SystemInfo.operatingSystem,
            ["Memory"] = SystemInfo.systemMemorySize,
            ["Graphics"] = SystemInfo.graphicsDeviceName,
            ["TargetFrameRate"] = Application.targetFrameRate,
            ["TimeScale"] = Time.timeScale
        };
    }
}
```

## 15. 总结

这个游戏化力量跟随系统设计文档提供了完整的技术实现方案，主要特点包括：

### 核心创新点：
1. **游戏化体验**：将枯燥的力量跟随实验转化为类似Flappy Bird的游戏
2. **自适应难度**：根据用户表现动态调整关卡难度
3. **实时反馈**：丰富的视觉和听觉反馈系统
4. **跨平台支持**：同时支持Android平板和Windows PC

### 技术架构优势：
1. **模块化设计**：清晰的模块分离，便于维护和扩展
2. **异常处理**：完善的错误处理和自动恢复机制
3. **数据管理**：多格式数据存储，便于后续分析
4. **性能优化**：针对移动设备的性能优化策略

### 用户体验设计：
1. **简化操作**：最小化用户配置，自动化流程
2. **直观反馈**：清晰的进度指示和性能评价
3. **个性化体验**：自适应难度和个人历史记录
4. **鼓励机制**：星级评定和鼓励性文字

该系统既保持了科学实验的严谨性，又大幅提升了用户的参与度和体验质量，为力量控制研究提供了创新的实验范式。dDate()
{
return System.IO.File.GetLastWriteTime(Application.dataPath).ToString("yyyy-MM-dd");
}

    private void ShowMessage(string title, string message)
    {
        MessageDialog.Instance.Show(title, message);
    }
}
```

## 11. 教程系统

### 11.1 交互式教程管理器
```csharp
public class TutorialManager : MonoBehaviour
{
    public static TutorialManager Instance;
    
    [Header("教程步骤")]
    public List<TutorialStep> tutorialSteps;
    public int currentStepIndex = 0;
    
    [Header("教程UI")]
    public GameObject tutorialPanel;
    public Text instructionText;
    public Text stepCounterText;
    public Button nextButton;
    public Button skipButton;
    public Image demonstrationImage;
    
    [Header("实时演示")]
    public GameObject demoGameArea;
    public Transform demoBird;
    public LineRenderer demoTargetPath;
    public Text demoForceText;
    
    [System.Serializable]
    public class TutorialStep
    {
        public string title;
        public string instruction;
        public Sprite demonstrationSprite;
        public bool requiresInteraction;
        public float minInteractionTime;
        public TutorialStepType stepType;
        public AnimationCurve demoTargetCurve;
    }
    
    public enum TutorialStepType
    {
        Introduction,       // 介绍
        BasicControl,       // 基础控制
        TargetFollowing,    // 目标跟随
        Scoring,           // 评分系统
        LevelProgression,  // 关卡进展
        Calibration        // 校准说明
    }
    
    private float stepStartTime;
    private bool stepCompleted = false;
    
    void Awake()
    {
        Instance = this;
    }
    
    void Start()
    {
        InitializeTutorial();
        SetupEventListeners();
    }
    
    void Update()
    {
        if (currentStepIndex < tutorialSteps.Count)
        {
            UpdateCurrentStep();
        }
    }
    
    private void InitializeTutorial()
    {
        CreateTutorialSteps();
        ShowStep(0);
    }
    
    private void CreateTutorialSteps()
    {
        tutorialSteps = new List<TutorialStep>
        {
            new TutorialStep
            {
                title = "欢迎来到握力游戏!",
                instruction = "这是一个有趣的握力控制游戏。你需要通过握紧或放松握力计来控制屏幕上的小鸟，让它跟随目标轨迹飞行。",
                stepType = TutorialStepType.Introduction,
                requiresInteraction = false,
                minInteractionTime = 3f
            },
            new TutorialStep
            {
                title = "基础控制",
                instruction = "握紧握力计，小鸟会向上飞；放松握力，小鸟会向下飞。试着控制你的握力来移动小鸟。",
                stepType = TutorialStepType.BasicControl,
                requiresInteraction = true,
                minInteractionTime = 10f
            },
            new TutorialStep
            {
                title = "跟随目标",
                instruction = "红色的线是目标轨迹。你需要控制握力让小鸟尽可能贴近这条红线飞行。越贴近目标，得分越高!",
                stepType = TutorialStepType.TargetFollowing,
                requiresInteraction = true,
                minInteractionTime = 15f,
                demoTargetCurve = CreateDemoTargetCurve()
            },
            new TutorialStep
            {
                title = "评分系统",
                instruction = "游戏会根据你的跟随精度、稳定性和反应速度来评分。完成关卡后会获得0-3颗星的评价。",
                stepType = TutorialStepType.Scoring,
                requiresInteraction = false,
                minInteractionTime = 5f
            },
            new TutorialStep
            {
                title = "关卡进展",
                instruction = "游戏包含多个关卡，难度会逐渐增加。系统会根据你的表现自动调整后续关卡的难度。",
                stepType = TutorialStepType.LevelProgression,
                requiresInteraction = false,
                minInteractionTime = 5f
            },
            new TutorialStep
            {
                title = "准备开始",
                instruction = "教程完成! 接下来我们需要测量你的最大握力(MVC)和静息状态，然后就可以开始游戏了。",
                stepType = TutorialStepType.Calibration,
                requiresInteraction = false,
                minInteractionTime = 3f
            }
        };
    }
    
    private void SetupEventListeners()
    {
        nextButton.onClick.AddListener(OnNextButtonClicked);
        skipButton.onClick.AddListener(OnSkipButtonClicked);
    }
    
    private void ShowStep(int stepIndex)
    {
        if (stepIndex >= tutorialSteps.Count) return;
        
        currentStepIndex = stepIndex;
        var step = tutorialSteps[stepIndex];
        stepStartTime = Time.time;
        stepCompleted = false;
        
        // 更新UI
        instructionText.text = step.instruction;
        stepCounterText.text = $"{stepIndex + 1} / {tutorialSteps.Count}";
        
        // 更新演示图片
        if (step.demonstrationSprite != null)
        {
            demonstrationImage.sprite = step.demonstrationSprite;
            demonstrationImage.gameObject.SetActive(true);
        }
        else
        {
            demonstrationImage.gameObject.SetActive(false);
        }
        
        // 设置按钮状态
        nextButton.interactable = !step.requiresInteraction;
        nextButton.GetComponentInChildren<Text>().text = 
            stepIndex == tutorialSteps.Count - 1 ? "开始校准" : "下一步";
        
        // 根据步骤类型设置演示
        SetupStepDemo(step);
    }
    
    private void SetupStepDemo(TutorialStep step)
    {
        switch (step.stepType)
        {
            case TutorialStepType.BasicControl:
                EnableBasicControlDemo();
                break;
                
            case TutorialStepType.TargetFollowing:
                EnableTargetFollowingDemo(step.demoTargetCurve);
                break;
                
            default:
                DisableDemo();
                break;
        }
    }
    
    private void EnableBasicControlDemo()
    {
        demoGameArea.SetActive(true);
        demoTargetPath.gameObject.SetActive(false);
        
        // 显示当前握力值
        demoForceText.gameObject.SetActive(true);
    }
    
    private void EnableTargetFollowingDemo(AnimationCurve targetCurve)
    {
        demoGameArea.SetActive(true);
        demoTargetPath.gameObject.SetActive(true);
        
        // 绘制演示目标曲线
        DrawDemoTargetPath(targetCurve);
    }
    
    private void DisableDemo()
    {
        demoGameArea.SetActive(false);
        demoForceText.gameObject.SetActive(false);
    }
    
    private void DrawDemoTargetPath(AnimationCurve curve)
    {
        int pointCount = 50;
        Vector3[] points = new Vector3[pointCount];
        
        for (int i = 0; i < pointCount; i++)
        {
            float t = (float)i / (pointCount - 1);
            float curveValue = curve.Evaluate(t);
            
            float x = Mathf.Lerp(-200f, 200f, t);
            float y = Mathf.Lerp(-100f, 100f, curveValue / 100f);
            
            points[i] = new Vector3(x, y, 0);
        }
        
        demoTargetPath.positionCount = pointCount;
        demoTargetPath.SetPositions(points);
    }
    
    private void UpdateCurrentStep()
    {
        var step = tutorialSteps[currentStepIndex];
        
        // 更新演示小鸟位置（如果演示激活）
        if (demoGameArea.activeInHierarchy)
        {
            UpdateDemoBirdPosition();
        }
        
        // 检查交互式步骤的完成条件
        if (step.requiresInteraction && !stepCompleted)
        {
            CheckInteractionCompletion(step);
        }
        
        // 检查最小时间要求
        bool timeRequirementMet = (Time.time - stepStartTime) >= step.minInteractionTime;
        
        if (timeRequirementMet && (!step.requiresInteraction || stepCompleted))
        {
            nextButton.interactable = true;
            
            if (step.requiresInteraction)
            {
                nextButton.GetComponent<Image>().color = Color.green;
                nextButton.GetComponentInChildren<Text>().text = "很好! 继续";
            }
        }
    }
    
    private void UpdateDemoBirdPosition()
    {
        if (BLEDeviceManager.Instance != null)
        {
            float currentForce = BLEDeviceManager.Instance.currentForce;
            
            // 更新小鸟Y位置
            float targetY = Mathf.Lerp(-100f, 100f, currentForce / 100f);
            Vector3 currentPos = demoBird.localPosition;
            demoBird.localPosition = Vector3.Lerp(currentPos, 
                new Vector3(currentPos.x, targetY, 0), Time.deltaTime * 5f);
            
            // 更新力值显示
            if (demoForceText.gameObject.activeInHierarchy)
            {
                demoForceText.text = $"当前握力: {currentForce:F1}%";
            }
        }
    }
    
    private void CheckInteractionCompletion(TutorialStep step)
    {
        switch (step.stepType)
        {
            case TutorialStepType.BasicControl:
                CheckBasicControlCompletion();
                break;
                
            case TutorialStepType.TargetFollowing:
                CheckTargetFollowingCompletion();
                break;
        }
    }
    
    private void CheckBasicControlCompletion()
    {
        if (BLEDeviceManager.Instance != null)
        {
            float currentForce = BLEDeviceManager.Instance.currentForce;
            
            // 检查用户是否尝试了不同的握力水平
            if (!stepCompleted)
            {
                // 简单检查：用户是否在10秒内尝试了高低不同的握力
                float timeSinceStart = Time.time - stepStartTime;
                if (timeSinceStart > 5f && (currentForce > 20f || currentForce < 5f))
                {
                    stepCompleted = true;
                    ShowCompletionEffect();
                }
            }
        }
    }
    
    private void CheckTargetFollowingCompletion()
    {
        if (BLEDeviceManager.Instance != null)
        {
            float currentForce = BLEDeviceManager.Instance.currentForce;
            float timeSinceStart = Time.time - stepStartTime;
            
            // 检查用户是否尝试跟随目标
            if (timeSinceStart > 10f)
            {
                stepCompleted = true;
                ShowCompletionEffect();
            }
        }
    }
    
    private void ShowCompletionEffect()
    {
        // 显示完成特效
        if (AudioManager.Instance != null)
        {
            AudioManager.Instance.PlaySFX(AudioManager.Instance.levelComplete);
        }
        
        // 可以添加粒子特效等
    }
    
    private void OnNextButtonClicked()
    {
        AudioManager.Instance.PlayButtonClick();
        
        if (currentStepIndex < tutorialSteps.Count - 1)
        {
            ShowStep(currentStepIndex + 1);
        }
        else
        {
            CompleteTutorial();
        }
    }
    
    private void OnSkipButtonClicked()
    {
        AudioManager.Instance.PlayButtonClick();
        
        ConfirmationDialog.Instance.Show(
            "跳过教程",
            "确定要跳过教程吗？你可以在设置中重新查看教程。",
            CompleteTutorial,
            null
        );
    }
    
    private void CompleteTutorial()
    {
        // 标记教程已完成
        PlayerPrefs.SetInt("TutorialCompleted", 1);
        PlayerPrefs.Save();
        
        // 转换到MVC校准
        GameStateManager.Instance.TransitionToState(GameState.MVCCalibration);
    }
    
    // 静态方法创建演示曲线
    private static AnimationCurve CreateDemoTargetCurve()
    {
        return new AnimationCurve(
            new Keyframe(0f, 10f),
            new Keyframe(0.3f, 30f),
            new Keyframe(0.6f, 20f),
            new Keyframe(1f, 40f)
        );
    }
    
    // 公共方法供其他地方调用
    public static bool IsTutorialCompleted()
    {
        return PlayerPrefs.GetInt("TutorialCompleted", 0) == 1;
    }
    
    public static void ResetTutorial()
    {
        PlayerPrefs.DeleteKey("TutorialCompleted");
        PlayerPrefs.Save();
    }
}
```

## 12. 异常处理和恢复系统

### 12.1 异常管理器
```csharp
public class ExceptionManager : MonoBehaviour
{
    public static ExceptionManager Instance;
    
    [Header("异常处理配置")]
    public bool enableGlobalExceptionHandling = true;
    public bool saveErrorLogs = true;
    public bool showErrorToUser = true;
    public int maxRetryAttempts = 3;
    
    [Header("恢复策略")]
    public float deviceReconnectInterval = 5f;
    public float autoSaveInterval = 30f;
    
    private Dictionary<string, int> retryCounters = new Dictionary<string, int>();
    private string errorLogPath;
    
    public UnityEvent<string> OnCriticalError;
    public UnityEvent OnDeviceDisconnected;
    public UnityEvent OnDeviceReconnected;
    
    void Awake()
    {
        Instance = this;
        DontDestroyOnLoad(gameObject);
        
        if (enableGlobalExceptionHandling)
        {
            Application.logMessageReceived += HandleException;
        }
        
        InitializeErrorLogging();
        StartCoroutine(DeviceConnectionMonitor());
        InvokeRepeating(nameof(AutoSaveCheck), autoSaveInterval, autoSaveInterval);
    }
    
    void OnDestroy()
    {
        if (enableGlobalExceptionHandling)
        {
            Application.logMessageReceived -= HandleException;
        }
    }
    
    private void InitializeErrorLogging()
    {
        if (saveErrorLogs)
        {
            string logDirectory = Path.Combine(Application.persistentDataPath, "ErrorLogs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }
            
            errorLogPath = Path.Combine(logDirectory, $"errors_{DateTime.Now:yyyyMMdd}.log");
        }
    }
    
    private void HandleException(string logString, string stackTrace, LogType type)
    {
        if (type == LogType.Exception || type == LogType.Error)
        {
            var errorInfo = new ErrorInfo
            {
                message = logString,
                stackTrace = stackTrace,
                timestamp = DateTime.Now,
                gameState = GameStateManager.Instance?.currentState.ToString() ?? "Unknown",
                participantId = DataManager.Instance?.currentParticipantId ?? "Unknown"
            };
            
            LogError(errorInfo);
            
            // 根据错误类型采取相应措施
            if (IsCriticalError(logString))
            {
                HandleCriticalError(errorInfo);
            }
            else
            {
                HandleNonCriticalError(errorInfo);
            }
        }
    }
    
    private bool IsCriticalError(string errorMessage)
    {
        string[] criticalKeywords = 
        {
            "NullReferenceException",
            "OutOfMemoryException",
            "StackOverflowException",
            "bluetooth",
            "device connection"
        };
        
        return criticalKeywords.Any(keyword => 
            errorMessage.ToLower().Contains(keyword.ToLower()));
    }
    
    private void HandleCriticalError(ErrorInfo errorInfo)
    {
        Debug.LogError($"检测到严重错误: {errorInfo.message}");
        
        // 立即保存当前数据
        try
        {
            DataManager.Instance?.SaveCurrentSession();
        }
        catch (Exception e)
        {
            Debug.LogError($"紧急保存数据失败: {e.Message}");
        }
        
        // 通知用户
        if (showErrorToUser)
        {
            ShowCriticalErrorDialog(errorInfo);
        }
        
        // 触发全局事件
        OnCriticalError?.Invoke(errorInfo.message);
    }
    
    private void HandleNonCriticalError(ErrorInfo errorInfo)
    {
        Debug.LogWarning($"检测到非严重错误: {errorInfo.message}");
        
        // 尝试自动恢复
        AttemptAutoRecovery(errorInfo);
    }
    
    private void AttemptAutoRecovery(ErrorInfo errorInfo)
    {
        string errorKey = GetErrorKey(errorInfo.message);
        
        if (!retryCounters.ContainsKey(errorKey))
        {
            retryCounters[errorKey] = 0;
        }
        
        retryCounters[errorKey]++;
        
        if (retryCounters[errorKey] <= maxRetryAttempts)
        {
            Debug.Log($"尝试自动恢复 (第{retryCounters[errorKey]}次): {errorKey}");
            
            // 根据错误类型执行相应的恢复操作
            if (errorInfo.message.ToLower().Contains("bluetooth"))
            {
                StartCoroutine(AttemptDeviceReconnection());
            }
            else if (errorInfo.message.ToLower().Contains("data"))
            {
                AttemptDataRecovery();
            }
        }
        else
        {
            Debug.LogWarning($"自动恢复失败，已达到最大重试次数: {errorKey}");
            retryCounters[errorKey] = 0; // 重置计数器
        }
    }
    
    private IEnumerator AttemptDeviceReconnection()
    {
        Debug.Log("尝试重新连接设备...");
        
        if (BLEDeviceManager.Instance != null)
        {
            bool reconnected = false;
            int attempts = 0;
            
            while (!reconnected && attempts < maxRetryAttempts)
            {
                attempts++;
                yield return new WaitForSeconds(deviceReconnectInterval);
                
                try
                {
                    reconnected = await BLEDeviceManager.Instance.ConnectToDevice();
                    
                    if (reconnected)
                    {
                        Debug.Log("设备重连成功!");
                        OnDeviceReconnected?.Invoke();
                        
                        if (showErrorToUser)
                        {
                            MessageDialog.Instance.Show("设备已重连", "设备连接已恢复，可以继续游戏。");
                        }
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"重连尝试失败: {e.Message}");
                }
            }
            
            if (!reconnected)
            {
                Debug.LogError("设备重连失败，需要用户手动处理");
                OnDeviceDisconnected?.Invoke();
                
                if (showErrorToUser)
                {
                    ShowDeviceConnectionErrorDialog();
                }
            }
        }
    }
    
    private void AttemptDataRecovery()
    {
        try
        {
            // 尝试恢复数据完整性
            DataManager.Instance?.SaveCurrentSession();
            Debug.Log("数据恢复操作完成");
        }
        catch (Exception e)
        {
            Debug.LogError($"数据恢复失败: {e.Message}");
        }
    }
    
    private IEnumerator DeviceConnectionMonitor()
    {
        while (true)
        {
            yield return new WaitForSeconds(5f); // 每5秒检查一次
            
            if (BLEDeviceManager.Instance != null)
            {
                bool wasConnected = BLEDeviceManager.Instance.isConnected;
                bool isCurrentlyConnected = BLEDeviceManager.Instance.CheckConnectionStatus();
                
                if (wasConnected && !isCurrentlyConnected)
                {
                    Debug.LogWarning("检测到设备断开连接");
                    OnDeviceDisconnected?.Invoke();
                    
                    // 暂停当前游戏
                    if (GameStateManager.Instance.currentState == GameState.GameLevels)
                    {
                        PauseGameDueToDisconnection();
                    }
                    
                    // 尝试自动重连
                    StartCoroutine(AttemptDeviceReconnection());
                }
            }
        }
    }
    
    private void PauseGameDueToDisconnection()
    {
        // 暂停游戏并显示等待重连界面
        Time.timeScale = 0f;
        
        ConnectionLostDialog.Instance.Show(
            "设备连接中断",
            "握力计连接已中断，正在尝试重新连接...",
            () => {
                // 用户选择手动重连
                StartCoroutine(AttemptDeviceReconnection());
            },
            () => {
                // 用户选择返回主菜单
                Time.timeScale = 1f;
                DataManager.Instance.SaveCurrentSession();
                GameStateManager.Instance.TransitionToState(GameState.MainMenu);
            }
        );
    }
    
    private void AutoSaveCheck()
    {
        try
        {
            if (DataManager.Instance != null && 
                GameStateManager.Instance.currentState == GameState.GameLevels)
            {
                DataManager.Instance.SaveCurrentSession();
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"自动保存失败: {e.Message}");
        }
    }
    
    private void LogError(ErrorInfo errorInfo)
    {
        if (saveErrorLogs && !string.IsNullOrEmpty(errorLogPath))
        {
            try
            {
                string logEntry = $"[{errorInfo.timestamp:yyyy-MM-dd HH:mm:ss}] " +
                    $"State: {errorInfo.gameState} | " +
                    $"Participant: {errorInfo.participantId} | " +
                    $"Error: {errorInfo.message}\n" +
                    $"StackTrace: {errorInfo.stackTrace}\n" +
                    $"---\n";
                
                File.AppendAllText(errorLogPath, logEntry);
            }
            catch (Exception e)
            {
                Debug.LogError($"写入错误日志失败: {e.Message}");
            }
        }
    }
    
    private void ShowCriticalErrorDialog(ErrorInfo errorInfo)
    {
        CriticalErrorDialog.Instance.Show(
            "严重错误",
            $"程序遇到严重错误，已自动保存当前数据。\n\n错误信息: {errorInfo.message}",
            () => {
                // 重启游戏
                SceneManager.LoadScene(0);
            },
            () => {
                // 退出程序
                Application.Quit();
            }
        );
    }
    
    private void ShowDeviceConnectionErrorDialog()
    {
        DeviceErrorDialog.Instance.Show(
            "设备连接失败",
            "无法连接到握力计设备。请检查设备是否开启并重试。",
            () => {
                // 重试连接
                StartCoroutine(AttemptDeviceReconnection());
            },
            () => {
                // 返回主菜单
                GameStateManager.Instance.TransitionToState(GameState.MainMenu);
            }
        );
    }
    
    private string GetErrorKey(string errorMessage)
    {
        // 生成错误的简化标识符
        if (errorMessage.Length > 50)
        {
            return errorMessage.Substring(0, 50);
        }
        return errorMessage;
    }
    
    // 数据结构
    [System.Serializable]
    public class ErrorInfo
    {
        public string message;
        public string stackTrace;
        public DateTime timestamp;
        public string gameState;
        public string participantId;
    }
    
    // 公共接口方法
    public void ReportCustomError(string errorMessage, string context = "")
    {
        var errorInfo = new ErrorInfo
        {
            message = errorMessage,
            stackTrace = context,
            timestamp = DateTime.Now,
            gameState = GameStateManager.Instance?.currentState.ToString() ?? "Unknown",
            participantId = DataManager.Instance?.currentParticipantId ?? "Unknown"
        };
        
        HandleNonCriticalError(errorInfo);
    }
    
    public void ForceDataSave()
    {
        try
        {
            DataManager.Instance?.SaveCurrentSession();
            Debug.Log("强制数据保存完成");
        }
        catch (Exception e)
        {
            Debug.LogError($"强制数据保存失败: {e.Message}");
        }
    }
    
    public List<string> GetRecentErrorLogs(int maxLines = 100)
    {
        var logs = new List<string>();
        
        if (File.Exists(errorLogPath))
        {
            try
            {
                var allLines = File.ReadAllLines(errorLogPath);
                int startIndex = Math.Max(0, allLines.Length - maxLines);
                
                for (int i = startIndex; i < allLines.Length; i++)
                {
                    logs.Add(allLines[i]);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"读取错误日志失败: {e.Message}");
            }
        }
        
        return logs;
    }
}
```

## 13. 部署配置

### 13.1 构建设置管理器
```csharp
public class BuildConfigurationManager : MonoBehaviour
{
    [Header("构建配置")]
    public BuildConfiguration currentConfig;
    
    [System.Serializable]
    public class BuildConfiguration
    {
        public string buildVersion;
        public BuildTarget targetPlatform;
        public bool developmentBuild;
        public bool enableDebugging;
        public bool enableProfiling;
        public ScriptingImplementation scriptingBackend;
        public ApiCompatibilityLevel apiCompatibilityLevel;
    }
    
#if UNITY_EDITOR
    [Header("编辑器工具")]
    public Button buildAndroidButton;
    public Button buildWindowsButton;
    public Button openBuildFolderButton;
    
    void Start()
    {
        if (buildAndroidButton != null)
            buildAndroidButton.onClick.AddListener(() => BuildForPlatform(BuildTarget.Android));
            
        if (buildWindowsButton != null)
            buildWindowsButton.onClick.AddListener(() => BuildForPlatform(BuildTarget.StandaloneWindows64));
            
        if (openBuildFolderButton != null)
            openBuildFolderButton.onClick.AddListener(OpenBuildFolder);
    }
    
    private void BuildForPlatform(BuildTarget target)
    {
        string buildPath = GetBuildPath(target);
        
        // 设置构建选项
        BuildPlayerOptions buildOptions = new BuildPlayerOptions
        {
            scenes = GetScenesInBuild(),
            locationPathName = buildPath,
            target = target,
            options = GetBuildOptions()
        };
        
        // 执行构建
        BuildReport report = BuildPipeline.BuildPlayer(buildOptions);
        
        if (report.summary.result == BuildResult.Succeeded)
        {
            Debug.Log($"构建成功: {buildPath}");
            EditorUtility.DisplayDialog("构建完成", $"构建成功完成!\n路径: {buildPath}", "确定");
        }
        else
        {
            Debug.LogError($"构建失败: {report.summary.totalErrors} 个错误");
            EditorUtility.DisplayDialog("构建失败", "构建过程中出现错误，请检查控制台输出。", "确定");
        }
    }
    
    private string[] GetScenesInBuild()
    {
        return EditorBuildSettings.scenes
            .Where(scene => scene.enabled)
            .Select(scene => scene.path)
            .ToArray();
    }
    
    private string GetBuil