using System;
using UnityEngine;
using UnityEngine.Events;

namespace ForceFollowingGame.Core
{
    /// <summary>
    /// 游戏状态管理器 - 控制整个游戏的状态流转
    /// </summary>
    public class GameStateManager : MonoBehaviour
    {
        public static GameStateManager Instance { get; private set; }

        public enum GameState
        {
            MainMenu,           // 主菜单（用户登录）
            DeviceConnection,   // 设备连接
            Tutorial,           // 教程
            MVCCalibration,     // MVC校准游戏
            RESTCalibration,    // REST校准游戏
            GameLevels,         // 游戏关卡
            LevelTransition,    // 关卡间休息
            Results,            // 最终结果
            Settings            // 设置界面
        }

        [Header("状态管理")]
        [SerializeField] private GameState currentState = GameState.MainMenu;
        
        [Header("状态转换事件")]
        public UnityEvent<GameState> OnStateChanged;
        public UnityEvent<GameState, GameState> OnStateTransition;

        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;

        private GameState previousState;

        void Awake()
        {
            // 单例模式
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeStateManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        void Start()
        {
            // 初始化到主菜单状态
            TransitionToState(GameState.MainMenu);
        }

        private void InitializeStateManager()
        {
            if (enableDebugLogs)
                Debug.Log("[GameStateManager] 游戏状态管理器初始化完成");
        }

        /// <summary>
        /// 转换到新状态
        /// </summary>
        /// <param name="newState">目标状态</param>
        public void TransitionToState(GameState newState)
        {
            if (currentState == newState)
            {
                if (enableDebugLogs)
                    Debug.LogWarning($"[GameStateManager] 尝试转换到相同状态: {newState}");
                return;
            }

            if (!ValidateStateTransition(currentState, newState))
            {
                Debug.LogError($"[GameStateManager] 无效的状态转换: {currentState} -> {newState}");
                return;
            }

            previousState = currentState;
            
            if (enableDebugLogs)
                Debug.Log($"[GameStateManager] 状态转换: {currentState} -> {newState}");

            // 退出当前状态
            ExitCurrentState();
            
            // 更新状态
            currentState = newState;
            
            // 进入新状态
            EnterNewState();
            
            // 触发事件
            OnStateTransition?.Invoke(previousState, currentState);
            OnStateChanged?.Invoke(currentState);
        }

        /// <summary>
        /// 验证状态转换是否有效
        /// </summary>
        private bool ValidateStateTransition(GameState from, GameState to)
        {
            // 定义有效的状态转换规则
            switch (from)
            {
                case GameState.MainMenu:
                    return to == GameState.DeviceConnection || to == GameState.Settings;
                    
                case GameState.DeviceConnection:
                    return to == GameState.Tutorial || to == GameState.MainMenu;
                    
                case GameState.Tutorial:
                    return to == GameState.MVCCalibration || to == GameState.MainMenu;
                    
                case GameState.MVCCalibration:
                    return to == GameState.RESTCalibration || to == GameState.MainMenu;
                    
                case GameState.RESTCalibration:
                    return to == GameState.GameLevels || to == GameState.MainMenu;
                    
                case GameState.GameLevels:
                    return to == GameState.LevelTransition || to == GameState.Results || to == GameState.MainMenu;
                    
                case GameState.LevelTransition:
                    return to == GameState.GameLevels || to == GameState.Results || to == GameState.MainMenu;
                    
                case GameState.Results:
                    return to == GameState.MainMenu;
                    
                case GameState.Settings:
                    return to == GameState.MainMenu;
                    
                default:
                    return false;
            }
        }

        /// <summary>
        /// 退出当前状态
        /// </summary>
        private void ExitCurrentState()
        {
            switch (currentState)
            {
                case GameState.MainMenu:
                    // 隐藏主菜单UI
                    break;
                    
                case GameState.DeviceConnection:
                    // 清理设备连接相关资源
                    break;
                    
                case GameState.Tutorial:
                    // 清理教程资源
                    break;
                    
                case GameState.MVCCalibration:
                    // 停止MVC校准
                    break;
                    
                case GameState.RESTCalibration:
                    // 停止REST校准
                    break;
                    
                case GameState.GameLevels:
                    // 暂停游戏逻辑
                    break;
                    
                case GameState.LevelTransition:
                    // 清理关卡间界面
                    break;
                    
                case GameState.Results:
                    // 清理结果界面
                    break;
                    
                case GameState.Settings:
                    // 保存设置
                    break;
            }
        }

        /// <summary>
        /// 进入新状态
        /// </summary>
        private void EnterNewState()
        {
            switch (currentState)
            {
                case GameState.MainMenu:
                    // 显示主菜单UI
                    break;
                    
                case GameState.DeviceConnection:
                    // 开始设备连接流程
                    break;
                    
                case GameState.Tutorial:
                    // 开始教程
                    break;
                    
                case GameState.MVCCalibration:
                    // 开始MVC校准
                    break;
                    
                case GameState.RESTCalibration:
                    // 开始REST校准
                    break;
                    
                case GameState.GameLevels:
                    // 开始游戏关卡
                    break;
                    
                case GameState.LevelTransition:
                    // 显示关卡间界面
                    break;
                    
                case GameState.Results:
                    // 显示最终结果
                    break;
                    
                case GameState.Settings:
                    // 显示设置界面
                    break;
            }
        }

        /// <summary>
        /// 获取当前状态
        /// </summary>
        public GameState GetCurrentState()
        {
            return currentState;
        }

        /// <summary>
        /// 获取上一个状态
        /// </summary>
        public GameState GetPreviousState()
        {
            return previousState;
        }

        /// <summary>
        /// 检查是否处于指定状态
        /// </summary>
        public bool IsInState(GameState state)
        {
            return currentState == state;
        }

        /// <summary>
        /// 检查是否可以转换到指定状态
        /// </summary>
        public bool CanTransitionTo(GameState targetState)
        {
            return ValidateStateTransition(currentState, targetState);
        }

        /// <summary>
        /// 强制转换状态（调试用）
        /// </summary>
        [ContextMenu("Force Transition to Main Menu")]
        public void ForceTransitionToMainMenu()
        {
            currentState = GameState.Settings; // 临时设置为可以转换到MainMenu的状态
            TransitionToState(GameState.MainMenu);
        }

        void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && currentState == GameState.GameLevels)
            {
                // 游戏暂停时的处理
                if (enableDebugLogs)
                    Debug.Log("[GameStateManager] 应用暂停，游戏状态保持");
            }
        }

        void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && currentState == GameState.GameLevels)
            {
                // 失去焦点时的处理
                if (enableDebugLogs)
                    Debug.Log("[GameStateManager] 应用失去焦点，游戏状态保持");
            }
        }
    }
}
