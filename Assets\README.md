# 游戏化力量跟随系统

## 项目概述

这是一个基于Unity开发的游戏化力量跟随实验系统，将传统的力量跟随实验转化为类似Flappy Bird的游戏体验。系统通过BLE握力计采集实时数据，让被试者在游戏中完成力量控制任务，提高参与度和数据质量。

## 主要特性

- 🎮 **游戏化设计**: 关卡制、评分系统、自适应难度
- 📱 **跨平台支持**: Android平板和Windows PC
- 🔗 **实时数据采集**: BLE握力计数据接收和处理
- 📊 **科学分析**: 详细的性能指标分析
- 💾 **数据管理**: 自动保存和CSV导出
- 🎯 **自适应难度**: 根据表现动态调整游戏难度

## 系统架构

### 核心模块
- **GameStateManager**: 游戏状态管理
- **BLEDeviceManager**: 蓝牙设备连接和数据处理
- **LevelManager**: 关卡管理和切换
- **PerformanceAnalyzer**: 性能分析和指标计算
- **DataManager**: 数据存储和导出

### 游戏机制
- **TrajectoryGenerator**: 轨迹生成系统
- **AdaptiveDifficultySystem**: 自适应难度调整
- **CalibrationSystem**: MVC和REST校准

### 用户界面
- **MainMenuUI**: 主菜单界面
- **DeviceConnectionUI**: 设备连接界面
- **CalibrationUI**: 校准界面
- **GameplayUI**: 主游戏界面
- **LevelTransitionUI**: 关卡间休息界面

## 文件结构

```
Assets/
├── Scripts/
│   ├── Core/                    # 核心系统
│   │   ├── GameStateManager.cs
│   │   ├── BLEDeviceManager.cs
│   │   ├── DataStructures.cs
│   │   └── GameManager.cs
│   ├── Game/                    # 游戏逻辑
│   │   ├── LevelManager.cs
│   │   ├── AdaptiveDifficultySystem.cs
│   │   └── TrajectoryGenerator.cs
│   ├── Calibration/             # 校准系统
│   │   ├── MVCCalibrationGame.cs
│   │   └── RESTCalibrationGame.cs
│   ├── UI/                      # 用户界面
│   │   ├── MainMenuUI.cs
│   │   ├── DeviceConnectionUI.cs
│   │   ├── GameplayUI.cs
│   │   └── LevelTransitionUI.cs
│   ├── Analysis/                # 数据分析
│   │   └── PerformanceAnalyzer.cs
│   └── Data/                    # 数据管理
│       └── DataManager.cs
├── Docs/                        # 文档
│   ├── 设计文档.md
│   ├── Unity场景设置指南.md
│   ├── 使用说明.md
│   └── 握力计demo/              # Python演示代码
└── README.md                    # 项目说明
```

## 快速开始

### 1. 环境要求
- Unity 2021.3 LTS 或更高版本
- BLE握力计设备 (MAC: 3C:AB:72:6F:68:6D)
- Android 5.0+ 或 Windows 10+

### 2. 项目设置
1. 克隆或下载项目到本地
2. 使用Unity Hub打开项目
3. 打开 `Assets/Scenes/MainScene.unity`
4. 按照 `Assets/Docs/Unity场景设置指南.md` 配置场景

### 3. 构建和运行
#### Android构建
```bash
# 在Unity中
File → Build Settings → Android
# 设置包名和权限
# 构建APK
```

#### Windows构建
```bash
# 在Unity中
File → Build Settings → PC, Mac & Linux Standalone
# 选择目标平台
# 构建可执行文件
```

### 4. 运行测试
1. 在Unity Editor中点击Play按钮
2. 系统自动启用模拟模式
3. 跟随界面提示完成测试流程

## 游戏流程

### 1. 主菜单
- 输入参与者ID
- 检查设备连接状态
- 开始游戏

### 2. 设备连接
- 自动搜索BLE握力计
- 建立连接
- 验证数据传输

### 3. 校准阶段
- **MVC校准**: 测量最大握力
- **REST校准**: 测量静息基线

### 4. 游戏关卡
- 5个预定义关卡
- 无限自适应关卡
- 实时性能反馈

### 5. 数据分析
- 关卡间结果显示
- 详细性能指标
- 数据自动保存

## 数据输出

### JSON格式 (完整数据)
```json
{
  "participantId": "P20240101_001",
  "sessionId": "20240101_143022",
  "mvcValue": 85.6,
  "restValue": 2.3,
  "levelResults": [...]
}
```

### CSV格式 (分析数据)
```csv
LevelNumber,LevelName,Timestamp,ActualForce,TargetForce,Deviation,InTolerance,EventMarker
1,热身运动,0.033,5.2,5.0,0.2,true,START
...
```

## 性能指标

- **跟随精度**: 基于与目标轨迹的偏差 (0-100%)
- **稳定性**: 基于力量输出的一致性 (0-100%)
- **反应时间**: 对目标变化的响应延迟 (毫秒)
- **最终得分**: 综合评分 (0-1000+分)
- **星级评定**: 0-3星评级

## 自适应难度

系统根据玩家最近5次表现自动调整难度:
- 目标维持75%的成功率
- 动态选择轨迹类型
- 调整MVC百分比和持续时间

## 开发特性

### 调试功能
- Editor模式自动模拟
- 详细日志系统
- 性能监控
- 快捷键支持

### 扩展性
- 模块化架构设计
- 事件驱动系统
- 可配置参数
- 插件式组件

## 技术栈

- **游戏引擎**: Unity 2021.3 LTS
- **编程语言**: C#
- **通信协议**: Bluetooth Low Energy (BLE)
- **数据格式**: JSON, CSV
- **平台支持**: Android, Windows

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目链接: [项目仓库地址]

## 致谢

- Unity Technologies - 游戏引擎
- 握力计硬件提供商
- 测试用户和反馈者

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的游戏化力量跟随系统
- 支持Android和Windows平台
- BLE设备连接和数据采集
- 自适应难度系统
- 详细的数据分析和导出

---

**注意**: 本系统仅用于科研目的，请确保符合相关伦理规范和数据保护要求。
